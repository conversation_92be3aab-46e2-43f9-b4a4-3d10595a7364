# 企业微信群转发助手 - 屏幕监控+OCR方案详细设计

## 方案概述

基于屏幕监控和OCR识别的企业微信群转发助手，通过定时截取企业微信聊天窗口，使用OCR技术识别新消息内容，实现智能转发功能。

## 核心优势

### ✅ 技术优势
- **无需API权限**：不依赖企业微信开放API
- **通用性强**：适用于任何聊天软件，不限于企业微信
- **兼容性好**：不依赖特定文件格式或数据库结构
- **可视化调试**：可以直观看到截图和识别结果
- **OCR技术成熟**：Tesseract OCR识别准确率高

### ✅ 业务优势
- **部署简单**：无需企业微信管理员权限
- **维护成本低**：不依赖外部服务和文件格式
- **扩展性好**：可扩展到其他聊天软件
- **用户友好**：可视化配置，易于调试和维护

## 技术架构设计

### 1. 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   企业微信客户端   │    │   文件监控服务    │    │   转发助手应用    │
│                │    │                │    │                │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │聊天记录DB │◄─┼────┼─►│FileWatcher│  │    │  │消息处理器  │  │
│  └───────────┘  │    │  └───────────┘  │    │  └───────────┘  │
│                │    │       │        │    │       │        │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │UI界面     │◄─┼────┼──┼─UI自动化  │◄─┼────┼─►│转发引擎   │  │
│  └───────────┘  │    │  └───────────┘  │    │  └───────────┘  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. 核心组件设计

#### 2.1 文件监控服务 (FileMonitorService)
```csharp
public class FileMonitorService
{
    private FileSystemWatcher _watcher;
    private string _wechatDataPath;
    private SQLiteConnection _connection;
    
    // 监控企业微信数据库文件变化
    public void StartMonitoring();
    
    // 处理文件变化事件
    private void OnFileChanged(object sender, FileSystemEventArgs e);
    
    // 读取新增消息
    private List<Message> ReadNewMessages();
}
```

#### 2.2 消息解析器 (MessageParser)
```csharp
public class MessageParser
{
    // 解析SQLite数据库中的消息记录
    public Message ParseMessage(DataRow row);
    
    // 提取订单号
    public string ExtractOrderNumber(string content);
    
    // 判断是否为目标群组消息
    public bool IsTargetGroup(string groupId);
}
```

#### 2.3 转发引擎 (ForwardingEngine)
```csharp
public class ForwardingEngine
{
    // 根据订单号查询目标群组
    public async Task<string> GetTargetGroup(string orderNumber);
    
    // 发送消息到目标群组
    public async Task<bool> SendMessage(string groupId, string content);
    
    // UI自动化发送消息
    private bool SendViaUIAutomation(string groupId, string content);
}
```

## 企业微信文件结构分析

### 1. 数据存储位置

企业微信聊天记录通常存储在以下位置：

```
Windows 10/11:
C:\Users\<USER>\Documents\WeChat Files\{企业微信号}\Msg\
或
C:\Users\<USER>\AppData\Roaming\Tencent\WeChat\{企业微信号}\Msg\

主要文件：
- MSG0.db          # 聊天记录数据库
- MSG1.db          # 聊天记录数据库（备份）
- MediaMSG.db      # 媒体消息数据库
- MicroMsg.db      # 联系人和群组信息
```

### 2. 数据库表结构

#### 消息表 (MSG)
```sql
CREATE TABLE MSG (
    localId INTEGER PRIMARY KEY,
    TalkerId INTEGER,           -- 会话ID（群组或个人）
    MsgSvrId INTEGER,          -- 服务器消息ID
    Type INTEGER,              -- 消息类型（1=文本，3=图片，等）
    SubType INTEGER,           -- 消息子类型
    IsSender INTEGER,          -- 是否为发送者（0=接收，1=发送）
    CreateTime INTEGER,        -- 创建时间（时间戳）
    Sequence INTEGER,          -- 消息序号
    StatusEx INTEGER,          -- 消息状态
    FlagEx INTEGER,            -- 标志位
    Status INTEGER,            -- 状态
    MsgServerSeq INTEGER,      -- 服务器序号
    MsgSequence INTEGER,       -- 消息序列
    StrTalker TEXT,           -- 会话标识符
    StrContent TEXT,          -- 消息内容
    DisplayContent TEXT       -- 显示内容
);
```

#### 联系人表 (Contact)
```sql
CREATE TABLE Contact (
    UserName TEXT PRIMARY KEY,  -- 用户名/群组ID
    Alias TEXT,                -- 别名
    EncryptUserName TEXT,      -- 加密用户名
    DelFlag INTEGER,           -- 删除标志
    Type INTEGER,              -- 类型（群组/个人）
    VerifyFlag INTEGER,        -- 验证标志
    Reserved1 INTEGER,         -- 保留字段1
    Reserved2 INTEGER,         -- 保留字段2
    Reserved3 TEXT,            -- 保留字段3
    Reserved4 TEXT,            -- 保留字段4
    Remark TEXT,               -- 备注
    NickName TEXT,             -- 昵称
    LabelIDList TEXT,          -- 标签ID列表
    DomainList TEXT,           -- 域名列表
    ChatRoomType INTEGER,      -- 聊天室类型
    PYInitial TEXT,            -- 拼音首字母
    QuanPin TEXT               -- 全拼
);
```

### 3. 文件监控策略

#### 3.1 监控配置
```csharp
var watcher = new FileSystemWatcher()
{
    Path = wechatDataPath,
    Filter = "MSG*.db",
    NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size,
    EnableRaisingEvents = true
};
```

#### 3.2 增量读取策略
```csharp
public class MessageTracker
{
    private long _lastProcessedId = 0;
    
    public List<Message> GetNewMessages()
    {
        var query = $@"
            SELECT * FROM MSG 
            WHERE localId > {_lastProcessedId} 
            AND Type = 1 
            AND IsSender = 0
            ORDER BY localId ASC";
            
        // 执行查询并更新_lastProcessedId
    }
}
```

## 实现细节

### 1. 文件路径自动检测

```csharp
public class WeChatPathDetector
{
    public string DetectWeChatDataPath()
    {
        var possiblePaths = new[]
        {
            Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "WeChat Files"),
            Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Tencent", "WeChat"),
            // 添加更多可能的路径
        };
        
        foreach (var basePath in possiblePaths)
        {
            if (Directory.Exists(basePath))
            {
                // 查找企业微信账号目录
                var accountDirs = Directory.GetDirectories(basePath)
                    .Where(d => Directory.Exists(Path.Combine(d, "Msg")))
                    .ToList();
                    
                if (accountDirs.Any())
                    return Path.Combine(accountDirs.First(), "Msg");
            }
        }
        
        throw new DirectoryNotFoundException("未找到企业微信数据目录");
    }
}
```

### 2. 数据库连接管理

```csharp
public class WeChatDatabaseManager
{
    private readonly string _connectionString;
    
    public WeChatDatabaseManager(string dbPath)
    {
        _connectionString = $"Data Source={dbPath};Version=3;Read Only=True;";
    }
    
    public async Task<List<Message>> ReadMessagesAsync(long fromId)
    {
        using var connection = new SQLiteConnection(_connectionString);
        await connection.OpenAsync();
        
        var command = new SQLiteCommand(
            "SELECT * FROM MSG WHERE localId > @fromId AND Type = 1 ORDER BY localId", 
            connection);
        command.Parameters.AddWithValue("@fromId", fromId);
        
        var messages = new List<Message>();
        using var reader = await command.ExecuteReaderAsync();
        
        while (await reader.ReadAsync())
        {
            messages.Add(ParseMessage(reader));
        }
        
        return messages;
    }
}
```

### 3. 消息发送实现

#### 3.1 剪贴板 + 快捷键方式
```csharp
public class ClipboardMessageSender
{
    public async Task<bool> SendMessage(string groupName, string content)
    {
        try
        {
            // 1. 激活企业微信窗口
            ActivateWeChatWindow();
            
            // 2. 搜索并打开目标群组
            await OpenTargetGroup(groupName);
            
            // 3. 复制消息到剪贴板
            Clipboard.SetText(content);
            
            // 4. 粘贴并发送
            SendKeys.SendWait("^v");  // Ctrl+V
            await Task.Delay(100);
            SendKeys.SendWait("{ENTER}");  // Enter
            
            return true;
        }
        catch (Exception ex)
        {
            Logger.LogError($"发送消息失败: {ex.Message}");
            return false;
        }
    }
}
```

#### 3.2 UI自动化方式
```csharp
public class UIAutomationSender
{
    public async Task<bool> SendMessage(string groupName, string content)
    {
        var automation = AutomationElement.RootElement;
        
        // 查找企业微信窗口
        var wechatWindow = automation.FindFirst(
            TreeScope.Children,
            new PropertyCondition(AutomationElement.NameProperty, "企业微信"));
            
        if (wechatWindow == null) return false;
        
        // 查找消息输入框
        var inputBox = wechatWindow.FindFirst(
            TreeScope.Descendants,
            new PropertyCondition(AutomationElement.ControlTypeProperty, ControlType.Edit));
            
        if (inputBox == null) return false;
        
        // 输入消息
        var valuePattern = inputBox.GetCurrentPattern(ValuePattern.Pattern) as ValuePattern;
        valuePattern?.SetValue(content);
        
        // 发送消息
        SendKeys.SendWait("{ENTER}");
        
        return true;
    }
}
```

## 配置文件设计

### 1. 应用配置 (appsettings.json)
```json
{
  "FileMonitor": {
    "WeChatDataPath": "",
    "MonitorInterval": 1000,
    "MaxRetryCount": 3,
    "EnableAutoDetection": true
  },
  "MessageProcessing": {
    "OrderNumberPatterns": [
      "^ORD\\d{8}$",
      "^\\d{10,}$",
      "^[A-Z]{2}\\d{6}$"
    ],
    "IgnoreOwnMessages": true,
    "ProcessHistoryMessages": false
  },
  "Forwarding": {
    "SendMethod": "Clipboard",
    "SendDelay": 500,
    "MaxQueueSize": 100,
    "EnableRetry": true
  }
}
```

### 2. 群组配置 (groups.json)
```json
{
  "SourceGroups": [
    {
      "Id": "source_group_1",
      "Name": "订单通知群",
      "WeChatGroupName": "订单通知群",
      "Enabled": true,
      "OrderNumberPatterns": ["^ORD\\d{8}$"]
    }
  ],
  "TargetGroups": [
    {
      "Id": "target_group_b",
      "Name": "B组处理群",
      "WeChatGroupName": "B组处理群",
      "Enabled": true
    },
    {
      "Id": "target_group_c", 
      "Name": "C组处理群",
      "WeChatGroupName": "C组处理群",
      "Enabled": true
    }
  ],
  "ForwardingRules": [
    {
      "SourceGroupId": "source_group_1",
      "ApiEndpoint": "https://api.example.com/getTargetGroup",
      "Enabled": true
    }
  ]
}
```

## 风险评估与应对

### 1. 技术风险

#### 1.1 文件锁定问题
- **风险**：企业微信正在使用数据库文件，无法读取
- **应对**：使用只读模式打开，实现重试机制

#### 1.2 数据库结构变化
- **风险**：企业微信更新可能改变数据库结构
- **应对**：版本检测机制，兼容多个版本的表结构

#### 1.3 消息发送失败
- **风险**：UI自动化可能因界面变化失效
- **应对**：多种发送方式备选，人工干预提示

### 2. 业务风险

#### 2.1 消息遗漏
- **风险**：程序重启时可能遗漏消息
- **应对**：状态持久化，断点续传机制

#### 2.2 重复发送
- **风险**：程序异常可能导致消息重复发送
- **应对**：消息去重机制，发送状态跟踪

## 开发优先级

### 第一阶段：核心功能 (1周)
1. 文件路径检测和数据库连接
2. 基础消息读取和解析
3. 简单的订单号识别
4. 剪贴板发送方式

### 第二阶段：完善功能 (1周)  
1. 文件监控和增量读取
2. 配置文件管理
3. 日志记录和错误处理
4. 基础UI界面

### 第三阶段：高级功能 (1周)
1. API集成和智能路由
2. UI自动化发送
3. 状态监控和统计
4. 完整的用户界面

这个方案相比API方案大大降低了技术复杂度，同时保持了功能的完整性和可靠性。
