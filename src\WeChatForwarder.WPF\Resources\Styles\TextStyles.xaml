<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 标题样式 -->
    <Style x:Key="HeaderText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="{StaticResource HeaderFontSize}"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
    </Style>

    <!-- 子标题样式 -->
    <Style x:Key="SubHeaderText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="{StaticResource SubHeaderFontSize}"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="Margin" Value="0,0,0,12"/>
    </Style>

    <!-- 正文样式 -->
    <Style x:Key="BodyText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="{StaticResource BodyFontSize}"/>
        <Setter Property="FontWeight" Value="Regular"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="LineHeight" Value="20"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- 说明文字样式 -->
    <Style x:Key="CaptionText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="{StaticResource CaptionFontSize}"/>
        <Setter Property="FontWeight" Value="Regular"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
    </Style>

    <!-- 成功状态文字 -->
    <Style x:Key="SuccessText" TargetType="TextBlock" BasedOn="{StaticResource BodyText}">
        <Setter Property="Foreground" Value="{StaticResource SuccessBrush}"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 警告状态文字 -->
    <Style x:Key="WarningText" TargetType="TextBlock" BasedOn="{StaticResource BodyText}">
        <Setter Property="Foreground" Value="{StaticResource WarningBrush}"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 错误状态文字 -->
    <Style x:Key="ErrorText" TargetType="TextBlock" BasedOn="{StaticResource BodyText}">
        <Setter Property="Foreground" Value="{StaticResource ErrorBrush}"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 信息状态文字 -->
    <Style x:Key="InfoText" TargetType="TextBlock" BasedOn="{StaticResource BodyText}">
        <Setter Property="Foreground" Value="{StaticResource InfoBrush}"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 数字显示样式 -->
    <Style x:Key="NumberText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="32"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- 标签样式 */
    <Style x:Key="LabelText" TargetType="TextBlock">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,0,8,0"/>
    </Style>

</ResourceDictionary>
