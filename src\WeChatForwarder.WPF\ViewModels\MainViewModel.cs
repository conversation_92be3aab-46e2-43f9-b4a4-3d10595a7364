using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using WeChatForwarder.Core.Interfaces;
using WeChatForwarder.Core.Models;
using WeChatForwarder.Infrastructure.Configuration;

namespace WeChatForwarder.WPF.ViewModels
{
    /// <summary>
    /// 主视图模型
    /// </summary>
    public partial class MainViewModel : ObservableObject
    {
        private readonly ILogger<MainViewModel> _logger;
        private readonly ConfigurationManager _configManager;
        private readonly IMessageProcessor? _messageProcessor;

        [ObservableProperty]
        private string _statusText = "就绪";

        [ObservableProperty]
        private bool _isConnected = false;

        [ObservableProperty]
        private int _todayMessageCount = 0;

        [ObservableProperty]
        private int _successfulForwards = 0;

        [ObservableProperty]
        private int _failedForwards = 0;

        [ObservableProperty]
        private double _successRate = 100.0;

        /// <summary>
        /// 实时消息列表
        /// </summary>
        public ObservableCollection<Message> RealtimeMessages { get; } = new();

        /// <summary>
        /// 群组配置
        /// </summary>
        public ObservableCollection<GroupConfig> Groups { get; } = new();

        /// <summary>
        /// 转发规则
        /// </summary>
        public ObservableCollection<ForwardingRule> ForwardingRules { get; } = new();

        public MainViewModel(
            ILogger<MainViewModel> logger, 
            ConfigurationManager configManager,
            IMessageProcessor? messageProcessor = null)
        {
            _logger = logger;
            _configManager = configManager;
            _messageProcessor = messageProcessor;

            // 初始化数据
            _ = InitializeAsync();
        }

        /// <summary>
        /// 初始化异步数据
        /// </summary>
        private async Task InitializeAsync()
        {
            try
            {
                _logger.LogInformation("开始初始化主视图模型");

                // 加载配置
                await LoadConfigurationAsync();

                // 初始化统计数据
                UpdateStatistics();

                StatusText = "初始化完成";
                _logger.LogInformation("主视图模型初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "初始化主视图模型时发生错误");
                StatusText = $"初始化失败：{ex.Message}";
            }
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        private async Task LoadConfigurationAsync()
        {
            try
            {
                var groupConfig = await _configManager.LoadGroupConfigurationAsync();
                
                Groups.Clear();
                foreach (var group in groupConfig.Groups)
                {
                    Groups.Add(group);
                }

                ForwardingRules.Clear();
                foreach (var rule in groupConfig.ForwardingRules)
                {
                    ForwardingRules.Add(rule);
                }

                _logger.LogInformation("配置加载完成，群组数：{GroupCount}，规则数：{RuleCount}", 
                    Groups.Count, ForwardingRules.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载配置时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 更新统计数据
        /// </summary>
        private void UpdateStatistics()
        {
            // 计算今日消息数（从实时消息列表中筛选今日消息）
            var today = DateTime.Today;
            TodayMessageCount = RealtimeMessages.Count(m => m.Timestamp.Date == today);

            // 计算成功和失败的转发数
            SuccessfulForwards = RealtimeMessages.Count(m => m.Status == MessageProcessStatus.Forwarded);
            FailedForwards = RealtimeMessages.Count(m => m.Status == MessageProcessStatus.Failed);

            // 计算成功率
            var totalProcessed = SuccessfulForwards + FailedForwards;
            SuccessRate = totalProcessed > 0 ? (double)SuccessfulForwards / totalProcessed * 100 : 100.0;
        }

        /// <summary>
        /// 添加新消息
        /// </summary>
        public void AddMessage(Message message)
        {
            try
            {
                // 在UI线程中添加消息
                App.Current.Dispatcher.Invoke(() =>
                {
                    RealtimeMessages.Insert(0, message);

                    // 限制显示的消息数量（保留最新的1000条）
                    while (RealtimeMessages.Count > 1000)
                    {
                        RealtimeMessages.RemoveAt(RealtimeMessages.Count - 1);
                    }

                    // 更新统计数据
                    UpdateStatistics();
                });

                _logger.LogDebug("添加新消息：{MessageId}，内容：{Content}", message.Id, message.Content);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "添加消息时发生错误：{MessageId}", message.Id);
            }
        }

        /// <summary>
        /// 更新消息状态
        /// </summary>
        public void UpdateMessageStatus(string messageId, MessageProcessStatus status, string? errorMessage = null)
        {
            try
            {
                App.Current.Dispatcher.Invoke(() =>
                {
                    var message = RealtimeMessages.FirstOrDefault(m => m.Id == messageId);
                    if (message != null)
                    {
                        message.Status = status;
                        message.ErrorMessage = errorMessage;
                        
                        // 更新统计数据
                        UpdateStatistics();
                    }
                });

                _logger.LogDebug("更新消息状态：{MessageId}，状态：{Status}", messageId, status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新消息状态时发生错误：{MessageId}", messageId);
            }
        }

        /// <summary>
        /// 连接命令
        /// </summary>
        [RelayCommand]
        private async Task ConnectAsync()
        {
            try
            {
                StatusText = "正在连接...";
                
                // TODO: 实现连接逻辑
                await Task.Delay(2000); // 模拟连接过程
                
                IsConnected = true;
                StatusText = "已连接";
                
                _logger.LogInformation("连接成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "连接失败");
                StatusText = $"连接失败：{ex.Message}";
                IsConnected = false;
            }
        }

        /// <summary>
        /// 断开连接命令
        /// </summary>
        [RelayCommand]
        private async Task DisconnectAsync()
        {
            try
            {
                StatusText = "正在断开连接...";
                
                // TODO: 实现断开连接逻辑
                await Task.Delay(1000);
                
                IsConnected = false;
                StatusText = "已断开连接";
                
                _logger.LogInformation("断开连接成功");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开连接失败");
                StatusText = $"断开连接失败：{ex.Message}";
            }
        }

        /// <summary>
        /// 刷新配置命令
        /// </summary>
        [RelayCommand]
        private async Task RefreshConfigurationAsync()
        {
            try
            {
                StatusText = "正在刷新配置...";
                await LoadConfigurationAsync();
                StatusText = "配置刷新完成";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新配置失败");
                StatusText = $"刷新配置失败：{ex.Message}";
            }
        }

        /// <summary>
        /// 清空消息列表命令
        /// </summary>
        [RelayCommand]
        private void ClearMessages()
        {
            try
            {
                RealtimeMessages.Clear();
                UpdateStatistics();
                StatusText = "消息列表已清空";
                _logger.LogInformation("消息列表已清空");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清空消息列表失败");
                StatusText = $"清空失败：{ex.Message}";
            }
        }
    }
}
