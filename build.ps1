# Build script for WeChat Forwarder project
param(
    [string]$Configuration = "Debug",
    [switch]$Clean,
    [switch]$Test,
    [switch]$Publish
)

Write-Host "Building WeChat Forwarder project..." -ForegroundColor Green
Write-Host "Configuration: $Configuration" -ForegroundColor Yellow

# Clean if requested
if ($Clean) {
    Write-Host "Cleaning solution..." -ForegroundColor Yellow
    if (Test-Path "bin") { Remove-Item -Recurse -Force "bin" }
    if (Test-Path "obj") { Remove-Item -Recurse -Force "obj" }
    Get-ChildItem -Recurse -Directory -Name "bin" | Remove-Item -Recurse -Force
    Get-ChildItem -Recurse -Directory -Name "obj" | Remove-Item -Recurse -Force
    Write-Host "Clean completed." -ForegroundColor Green
}

# Check if .NET SDK is available
try {
    $dotnetVersion = dotnet --version
    Write-Host "Using .NET SDK version: $dotnetVersion" -ForegroundColor Cyan
} catch {
    Write-Host "Error: .NET SDK not found. Please install .NET 7.0 SDK." -ForegroundColor Red
    Write-Host "Download from: https://dotnet.microsoft.com/download" -ForegroundColor Yellow
    exit 1
}

# Restore packages
Write-Host "Restoring NuGet packages..." -ForegroundColor Yellow
try {
    dotnet restore WeChatForwarder.sln
    if ($LASTEXITCODE -ne 0) {
        throw "Package restore failed"
    }
    Write-Host "Package restore completed." -ForegroundColor Green
} catch {
    Write-Host "Error: Failed to restore packages - $_" -ForegroundColor Red
    exit 1
}

# Build solution
Write-Host "Building solution..." -ForegroundColor Yellow
try {
    dotnet build WeChatForwarder.sln --configuration $Configuration --no-restore
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    Write-Host "Build completed successfully." -ForegroundColor Green
} catch {
    Write-Host "Error: Build failed - $_" -ForegroundColor Red
    exit 1
}

# Run tests if requested
if ($Test) {
    Write-Host "Running tests..." -ForegroundColor Yellow
    try {
        dotnet test WeChatForwarder.sln --configuration $Configuration --no-build --verbosity normal
        if ($LASTEXITCODE -ne 0) {
            throw "Tests failed"
        }
        Write-Host "All tests passed." -ForegroundColor Green
    } catch {
        Write-Host "Error: Tests failed - $_" -ForegroundColor Red
        exit 1
    }
}

# Publish if requested
if ($Publish) {
    Write-Host "Publishing application..." -ForegroundColor Yellow
    $publishDir = "publish"
    
    if (Test-Path $publishDir) {
        Remove-Item -Recurse -Force $publishDir
    }
    
    try {
        dotnet publish src/WeChatForwarder.WPF/WeChatForwarder.WPF.csproj `
            --configuration Release `
            --output $publishDir `
            --self-contained false `
            --no-restore
            
        if ($LASTEXITCODE -ne 0) {
            throw "Publish failed"
        }
        
        Write-Host "Application published to: $publishDir" -ForegroundColor Green
    } catch {
        Write-Host "Error: Publish failed - $_" -ForegroundColor Red
        exit 1
    }
}

Write-Host "Build script completed successfully!" -ForegroundColor Green

# Display next steps
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Configure WeChat API settings in config/appsettings.json" -ForegroundColor White
Write-Host "2. Set up group configurations in config/groups.json" -ForegroundColor White
Write-Host "3. Run the application: dotnet run --project src/WeChatForwarder.WPF" -ForegroundColor White

if ($Publish) {
    Write-Host "4. Deploy the published files from the '$publishDir' directory" -ForegroundColor White
}
