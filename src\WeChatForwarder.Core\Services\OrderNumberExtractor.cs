using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using WeChatForwarder.Core.Interfaces;
using WeChatForwarder.Core.Models;

namespace WeChatForwarder.Core.Services
{
    /// <summary>
    /// 订单号提取器实现
    /// </summary>
    public class OrderNumberExtractor : IOrderNumberExtractor
    {
        private readonly ILogger<OrderNumberExtractor> _logger;
        private readonly List<OrderExtractionRule> _rules;

        public OrderNumberExtractor(ILogger<OrderNumberExtractor> logger)
        {
            _logger = logger;
            _rules = GetDefaultRules();
        }

        /// <summary>
        /// 从消息中提取订单号
        /// </summary>
        public async Task<string?> ExtractOrderNumberAsync(string message, string groupId)
        {
            if (string.IsNullOrWhiteSpace(message))
            {
                return null;
            }

            try
            {
                var applicableRules = await GetExtractionRulesAsync(groupId);
                var sortedRules = applicableRules
                    .Where(r => r.IsEnabled)
                    .OrderBy(r => r.Priority)
                    .ToList();

                foreach (var rule in sortedRules)
                {
                    var match = Regex.Match(message, rule.Pattern, RegexOptions.IgnoreCase);
                    if (match.Success)
                    {
                        var orderNumber = match.Groups.Count > 1 ? match.Groups[1].Value : match.Value;
                        
                        _logger.LogDebug("订单号提取成功: {OrderNumber}, 使用规则: {RuleName}", orderNumber, rule.Name);
                        return orderNumber.Trim();
                    }
                }

                _logger.LogDebug("未能从消息中提取到订单号: {Message}", message);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "提取订单号时发生错误: {Message}", message);
                return null;
            }
        }

        /// <summary>
        /// 验证订单号格式
        /// </summary>
        public bool ValidateOrderNumber(string orderNumber, string pattern)
        {
            if (string.IsNullOrWhiteSpace(orderNumber) || string.IsNullOrWhiteSpace(pattern))
            {
                return false;
            }

            try
            {
                return Regex.IsMatch(orderNumber, pattern, RegexOptions.IgnoreCase);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证订单号格式时发生错误: {OrderNumber}, Pattern: {Pattern}", orderNumber, pattern);
                return false;
            }
        }

        /// <summary>
        /// 获取适用的提取规则
        /// </summary>
        public async Task<IEnumerable<OrderExtractionRule>> GetExtractionRulesAsync(string groupId)
        {
            // 这里可以从配置文件或数据库加载规则
            // 目前返回默认规则和适用于指定群组的规则
            return await Task.FromResult(_rules.Where(r => 
                r.ApplicableGroupIds.Count == 0 || 
                r.ApplicableGroupIds.Contains(groupId)));
        }

        /// <summary>
        /// 获取默认提取规则
        /// </summary>
        private static List<OrderExtractionRule> GetDefaultRules()
        {
            return new List<OrderExtractionRule>
            {
                new OrderExtractionRule
                {
                    Id = "default_order_1",
                    Name = "标准订单号格式1",
                    Pattern = @"订单号[：:\s]*([A-Z0-9]{8,20})",
                    Priority = 1,
                    Description = "匹配'订单号：XXXXXXXX'格式"
                },
                new OrderExtractionRule
                {
                    Id = "default_order_2", 
                    Name = "标准订单号格式2",
                    Pattern = @"单号[：:\s]*([A-Z0-9]{8,20})",
                    Priority = 2,
                    Description = "匹配'单号：XXXXXXXX'格式"
                },
                new OrderExtractionRule
                {
                    Id = "default_order_3",
                    Name = "数字订单号",
                    Pattern = @"\b(\d{10,20})\b",
                    Priority = 3,
                    Description = "匹配10-20位纯数字订单号"
                },
                new OrderExtractionRule
                {
                    Id = "default_order_4",
                    Name = "字母数字组合",
                    Pattern = @"\b([A-Z]{2,4}\d{8,16})\b",
                    Priority = 4,
                    Description = "匹配字母+数字组合订单号"
                },
                new OrderExtractionRule
                {
                    Id = "default_order_5",
                    Name = "带前缀订单号",
                    Pattern = @"(?:ORD|ORDER|订单)[：:\s]*([A-Z0-9]{6,20})",
                    Priority = 5,
                    Description = "匹配带ORD/ORDER前缀的订单号"
                }
            };
        }
    }
}
