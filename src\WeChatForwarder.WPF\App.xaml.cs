using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Serilog;
using System.Windows;
using WeChatForwarder.Core.Interfaces;
using WeChatForwarder.Core.Services;
using WeChatForwarder.Infrastructure.Configuration;
using WeChatForwarder.Infrastructure.Http;
using WeChatForwarder.WPF.ViewModels;

namespace WeChatForwarder.WPF
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        private IHost? _host;

        protected override async void OnStartup(StartupEventArgs e)
        {
            // 配置Serilog
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.Console()
                .WriteTo.File("logs/app-.log", rollingInterval: RollingInterval.Day, retainedFileCountLimit: 30)
                .CreateLogger();

            try
            {
                // 创建主机
                _host = CreateHostBuilder().Build();

                // 启动主机
                await _host.StartAsync();

                // 显示主窗口
                var mainWindow = _host.Services.GetRequiredService<MainWindow>();
                mainWindow.Show();

                base.OnStartup(e);
            }
            catch (Exception ex)
            {
                Log.Fatal(ex, "应用程序启动失败");
                MessageBox.Show($"应用程序启动失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }

            Log.CloseAndFlush();
            base.OnExit(e);
        }

        private IHostBuilder CreateHostBuilder()
        {
            return Host.CreateDefaultBuilder()
                .UseSerilog()
                .ConfigureServices((context, services) =>
                {
                    // 注册配置管理
                    services.AddSingleton<ConfigurationManager>();

                    // 注册HTTP客户端
                    services.AddSingleton<HttpClientFactory>();
                    services.AddHttpClient();

                    // 注册核心服务
                    services.AddScoped<IOrderNumberExtractor, OrderNumberExtractor>();
                    services.AddScoped<IMessageProcessor, MessageProcessor>();
                    
                    // TODO: 注册其他服务
                    // services.AddScoped<IApiService, ApiService>();
                    // services.AddScoped<IForwardingService, ForwardingService>();
                    // services.AddScoped<IWeChatService, WeChatService>();

                    // 注册ViewModels
                    services.AddTransient<MainViewModel>();

                    // 注册Windows
                    services.AddTransient<MainWindow>();
                });
        }
    }
}
