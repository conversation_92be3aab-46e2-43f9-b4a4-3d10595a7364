using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using WeChatForwarder.Core.Services;
using Xunit;

namespace WeChatForwarder.Tests
{
    /// <summary>
    /// 订单号提取器测试
    /// </summary>
    public class OrderNumberExtractorTests
    {
        private readonly Mock<ILogger<OrderNumberExtractor>> _loggerMock;
        private readonly OrderNumberExtractor _extractor;

        public OrderNumberExtractorTests()
        {
            _loggerMock = new Mock<ILogger<OrderNumberExtractor>>();
            _extractor = new OrderNumberExtractor(_loggerMock.Object);
        }

        [Theory]
        [InlineData("订单号：ORD12345678", "ORD12345678")]
        [InlineData("订单号: ABC123456789", "ABC123456789")]
        [InlineData("单号：1234567890123456", "1234567890123456")]
        [InlineData("ORDER: XYZ987654321", "XYZ987654321")]
        public async Task ExtractOrderNumberAsync_ShouldExtractCorrectOrderNumber(string message, string expectedOrderNumber)
        {
            // Act
            var result = await _extractor.ExtractOrderNumberAsync(message, "test_group");

            // Assert
            result.Should().Be(expectedOrderNumber);
        }

        [Theory]
        [InlineData("这是一条普通消息")]
        [InlineData("没有订单号的消息")]
        [InlineData("")]
        [InlineData(null)]
        public async Task ExtractOrderNumberAsync_ShouldReturnNull_WhenNoOrderNumberFound(string message)
        {
            // Act
            var result = await _extractor.ExtractOrderNumberAsync(message, "test_group");

            // Assert
            result.Should().BeNull();
        }

        [Theory]
        [InlineData("1234567890", @"\d{10}", true)]
        [InlineData("ABC123", @"[A-Z]{3}\d{3}", true)]
        [InlineData("123", @"\d{10}", false)]
        [InlineData("abc123", @"[A-Z]{3}\d{3}", false)]
        public void ValidateOrderNumber_ShouldReturnCorrectResult(string orderNumber, string pattern, bool expected)
        {
            // Act
            var result = _extractor.ValidateOrderNumber(orderNumber, pattern);

            // Assert
            result.Should().Be(expected);
        }

        [Fact]
        public async Task GetExtractionRulesAsync_ShouldReturnDefaultRules()
        {
            // Act
            var rules = await _extractor.GetExtractionRulesAsync("test_group");

            // Assert
            rules.Should().NotBeEmpty();
            rules.Should().OnlyContain(r => r.IsEnabled);
        }

        [Theory]
        [InlineData("客户订单号：ORD20231201001，请及时处理", "ORD20231201001")]
        [InlineData("新订单提醒：单号 ABC123456789", "ABC123456789")]
        [InlineData("订单信息：ORDER-XYZ987654321，金额：1000元", "XYZ987654321")]
        public async Task ExtractOrderNumberAsync_ShouldHandleComplexMessages(string message, string expectedOrderNumber)
        {
            // Act
            var result = await _extractor.ExtractOrderNumberAsync(message, "test_group");

            // Assert
            result.Should().Be(expectedOrderNumber);
        }

        [Fact]
        public async Task ExtractOrderNumberAsync_ShouldReturnFirstMatch_WhenMultipleOrderNumbers()
        {
            // Arrange
            var message = "订单号：ORD123456 和 单号：ABC789012";

            // Act
            var result = await _extractor.ExtractOrderNumberAsync(message, "test_group");

            // Assert
            result.Should().Be("ORD123456"); // 应该返回第一个匹配的订单号
        }
    }
}
