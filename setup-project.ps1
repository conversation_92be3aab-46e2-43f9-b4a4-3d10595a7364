# WeChat Forwarder Project Initialization Script
# PowerShell script to create project structure and initialize code

param(
    [string]$ProjectName = "WeChatForwarder",
    [string]$RootPath = "."
)

Write-Host "Starting WeChat Forwarder project creation..." -ForegroundColor Green

# Create root directory structure
$directories = @(
    "src",
    "src\$ProjectName.WPF",
    "src\$ProjectName.WPF\Views",
    "src\$ProjectName.WPF\ViewModels",
    "src\$ProjectName.WPF\Controls",
    "src\$ProjectName.WPF\Converters",
    "src\$ProjectName.WPF\Resources",
    "src\$ProjectName.WPF\Resources\Styles",
    "src\$ProjectName.WPF\Resources\Images",
    "src\$ProjectName.WPF\Resources\Themes",

    "src\$ProjectName.Core",
    "src\$ProjectName.Core\Services",
    "src\$ProjectName.Core\Models",
    "src\$ProjectName.Core\Interfaces",
    "src\$ProjectName.Core\Exceptions",
    "src\$ProjectName.Core\Extensions",

    "src\$ProjectName.Infrastructure",
    "src\$ProjectName.Infrastructure\Data",
    "src\$ProjectName.Infrastructure\Logging",
    "src\$ProjectName.Infrastructure\Http",
    "src\$ProjectName.Infrastructure\Cache",

    "src\$ProjectName.WeChat",
    "src\$ProjectName.WeChat\Api",
    "src\$ProjectName.WeChat\Api\ApiModels",
    "src\$ProjectName.WeChat\Webhook",
    "src\$ProjectName.WeChat\Authentication",

    "src\$ProjectName.Common",
    "src\$ProjectName.Common\Constants",
    "src\$ProjectName.Common\Helpers",
    "src\$ProjectName.Common\Utilities",
    "src\$ProjectName.Common\Configuration",

    "tests",
    "tests\$ProjectName.Core.Tests",
    "tests\$ProjectName.Infrastructure.Tests",
    "tests\$ProjectName.WeChat.Tests",
    "tests\$ProjectName.Integration.Tests",

    "docs",
    "docs\api",
    "docs\user-guide",
    "docs\deployment",
    "docs\architecture",

    "scripts",
    "config",
    "assets",
    "assets\icons",
    "assets\images",
    "assets\templates"
)

Write-Host "Creating directory structure..." -ForegroundColor Yellow
foreach ($dir in $directories) {
    $fullPath = Join-Path $RootPath $dir
    if (!(Test-Path $fullPath)) {
        New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
        Write-Host "  Created directory: $dir" -ForegroundColor Gray
    }
}

# Create solution file
Write-Host "Creating solution file..." -ForegroundColor Yellow
$slnPath = Join-Path $RootPath "$ProjectName.sln"
if (!(Test-Path $slnPath)) {
    dotnet new sln -n $ProjectName -o $RootPath
}

# Create project files
Write-Host "Creating project files..." -ForegroundColor Yellow

# WPF主项目
$wpfProjectPath = Join-Path $RootPath "src\$ProjectName.WPF"
if (!(Test-Path "$wpfProjectPath\$ProjectName.WPF.csproj")) {
    dotnet new wpf -n "$ProjectName.WPF" -o $wpfProjectPath
    dotnet sln $slnPath add "$wpfProjectPath\$ProjectName.WPF.csproj"
}

# Core类库
$coreProjectPath = Join-Path $RootPath "src\$ProjectName.Core"
if (!(Test-Path "$coreProjectPath\$ProjectName.Core.csproj")) {
    dotnet new classlib -n "$ProjectName.Core" -o $coreProjectPath
    dotnet sln $slnPath add "$coreProjectPath\$ProjectName.Core.csproj"
}

# Infrastructure类库
$infraProjectPath = Join-Path $RootPath "src\$ProjectName.Infrastructure"
if (!(Test-Path "$infraProjectPath\$ProjectName.Infrastructure.csproj")) {
    dotnet new classlib -n "$ProjectName.Infrastructure" -o $infraProjectPath
    dotnet sln $slnPath add "$infraProjectPath\$ProjectName.Infrastructure.csproj"
}

# WeChat类库
$wechatProjectPath = Join-Path $RootPath "src\$ProjectName.WeChat"
if (!(Test-Path "$wechatProjectPath\$ProjectName.WeChat.csproj")) {
    dotnet new classlib -n "$ProjectName.WeChat" -o $wechatProjectPath
    dotnet sln $slnPath add "$wechatProjectPath\$ProjectName.WeChat.csproj"
}

# Common类库
$commonProjectPath = Join-Path $RootPath "src\$ProjectName.Common"
if (!(Test-Path "$commonProjectPath\$ProjectName.Common.csproj")) {
    dotnet new classlib -n "$ProjectName.Common" -o $commonProjectPath
    dotnet sln $slnPath add "$commonProjectPath\$ProjectName.Common.csproj"
}

# 测试项目
$testProjects = @(
    "$ProjectName.Core.Tests",
    "$ProjectName.Infrastructure.Tests", 
    "$ProjectName.WeChat.Tests",
    "$ProjectName.Integration.Tests"
)

foreach ($testProject in $testProjects) {
    $testProjectPath = Join-Path $RootPath "tests\$testProject"
    if (!(Test-Path "$testProjectPath\$testProject.csproj")) {
        dotnet new xunit -n $testProject -o $testProjectPath
        dotnet sln $slnPath add "$testProjectPath\$testProject.csproj"
    }
}

Write-Host "Adding project references..." -ForegroundColor Yellow

# Add project reference relationships
dotnet add "$wpfProjectPath\$ProjectName.WPF.csproj" reference "$coreProjectPath\$ProjectName.Core.csproj"
dotnet add "$wpfProjectPath\$ProjectName.WPF.csproj" reference "$infraProjectPath\$ProjectName.Infrastructure.csproj"
dotnet add "$wpfProjectPath\$ProjectName.WPF.csproj" reference "$wechatProjectPath\$ProjectName.WeChat.csproj"
dotnet add "$wpfProjectPath\$ProjectName.WPF.csproj" reference "$commonProjectPath\$ProjectName.Common.csproj"

dotnet add "$coreProjectPath\$ProjectName.Core.csproj" reference "$commonProjectPath\$ProjectName.Common.csproj"

dotnet add "$infraProjectPath\$ProjectName.Infrastructure.csproj" reference "$coreProjectPath\$ProjectName.Core.csproj"
dotnet add "$infraProjectPath\$ProjectName.Infrastructure.csproj" reference "$commonProjectPath\$ProjectName.Common.csproj"

dotnet add "$wechatProjectPath\$ProjectName.WeChat.csproj" reference "$coreProjectPath\$ProjectName.Core.csproj"
dotnet add "$wechatProjectPath\$ProjectName.WeChat.csproj" reference "$infraProjectPath\$ProjectName.Infrastructure.csproj"
dotnet add "$wechatProjectPath\$ProjectName.WeChat.csproj" reference "$commonProjectPath\$ProjectName.Common.csproj"

# Add test project references
dotnet add "tests\$ProjectName.Core.Tests\$ProjectName.Core.Tests.csproj" reference "$coreProjectPath\$ProjectName.Core.csproj"
dotnet add "tests\$ProjectName.Infrastructure.Tests\$ProjectName.Infrastructure.Tests.csproj" reference "$infraProjectPath\$ProjectName.Infrastructure.csproj"
dotnet add "tests\$ProjectName.WeChat.Tests\$ProjectName.WeChat.Tests.csproj" reference "$wechatProjectPath\$ProjectName.WeChat.csproj"

Write-Host "Adding NuGet packages..." -ForegroundColor Yellow

# WPF项目包
dotnet add "$wpfProjectPath\$ProjectName.WPF.csproj" package Microsoft.Extensions.Hosting
dotnet add "$wpfProjectPath\$ProjectName.WPF.csproj" package Microsoft.Extensions.DependencyInjection
dotnet add "$wpfProjectPath\$ProjectName.WPF.csproj" package CommunityToolkit.Mvvm
dotnet add "$wpfProjectPath\$ProjectName.WPF.csproj" package MaterialDesignThemes

# Core项目包
dotnet add "$coreProjectPath\$ProjectName.Core.csproj" package Microsoft.Extensions.DependencyInjection.Abstractions
dotnet add "$coreProjectPath\$ProjectName.Core.csproj" package Microsoft.Extensions.Logging.Abstractions

# Infrastructure项目包
dotnet add "$infraProjectPath\$ProjectName.Infrastructure.csproj" package Microsoft.Extensions.Http
dotnet add "$infraProjectPath\$ProjectName.Infrastructure.csproj" package Polly
dotnet add "$infraProjectPath\$ProjectName.Infrastructure.csproj" package Polly.Extensions.Http
dotnet add "$infraProjectPath\$ProjectName.Infrastructure.csproj" package Serilog
dotnet add "$infraProjectPath\$ProjectName.Infrastructure.csproj" package Serilog.Extensions.Hosting
dotnet add "$infraProjectPath\$ProjectName.Infrastructure.csproj" package Serilog.Sinks.File
dotnet add "$infraProjectPath\$ProjectName.Infrastructure.csproj" package Serilog.Sinks.Console
dotnet add "$infraProjectPath\$ProjectName.Infrastructure.csproj" package Microsoft.EntityFrameworkCore.Sqlite
dotnet add "$infraProjectPath\$ProjectName.Infrastructure.csproj" package Newtonsoft.Json

# WeChat项目包
dotnet add "$wechatProjectPath\$ProjectName.WeChat.csproj" package Microsoft.AspNetCore.Hosting
dotnet add "$wechatProjectPath\$ProjectName.WeChat.csproj" package Microsoft.AspNetCore.Mvc

# 测试项目包
foreach ($testProject in $testProjects) {
    $testProjectPath = Join-Path $RootPath "tests\$testProject"
    dotnet add "$testProjectPath\$testProject.csproj" package Moq
    dotnet add "$testProjectPath\$testProject.csproj" package FluentAssertions
}

# Create configuration files
Write-Host "Creating configuration files..." -ForegroundColor Yellow

# appsettings.json
$appSettingsContent = @"
{
  "WeChat": {
    "CorpId": "",
    "CorpSecret": "",
    "AgentId": "",
    "WebhookUrl": "http://localhost:8080/webhook"
  },
  "Api": {
    "BaseUrl": "",
    "Timeout": 30,
    "RetryCount": 3
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning"
    },
    "File": {
      "Path": "logs/app-.log",
      "RollingInterval": "Day",
      "RetainedFileCountLimit": 30
    }
  }
}
"@

$appSettingsPath = Join-Path $RootPath "config\appsettings.json"
$appSettingsContent | Out-File -FilePath $appSettingsPath -Encoding UTF8

# groups.json
$groupsConfigContent = @"
{
  "Groups": [
    {
      "Id": "group_a",
      "Name": "源群组A",
      "ChatId": "",
      "IsSource": true
    },
    {
      "Id": "group_b", 
      "Name": "目标群组B",
      "ChatId": "",
      "IsSource": false
    }
  ],
  "ForwardingRules": [
    {
      "SourceGroupId": "group_a",
      "OrderNumberPattern": "^ORD\\d{8}$",
      "ApiEndpoint": "/api/getGroupMapping"
    }
  ]
}
"@

$groupsConfigPath = Join-Path $RootPath "config\groups.json"
$groupsConfigContent | Out-File -FilePath $groupsConfigPath -Encoding UTF8

# Create .gitignore
Write-Host "Creating .gitignore file..." -ForegroundColor Yellow
$gitignoreContent = @"
# Build results
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
x64/
x86/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
bld/
[Bb]in/
[Oo]bj/
[Ll]og/

# Visual Studio
.vs/
*.user
*.suo
*.userosscache
*.sln.docstates

# User-specific files
*.rsuser
*.suo
*.user
*.userosscache
*.sln.docstates

# Configuration files with sensitive data
config/appsettings.Production.json
config/secrets.json

# Log files
logs/
*.log

# Cache files
*.cache

# NuGet
*.nupkg
*.snupkg
.nuget/

# Test results
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*

# Coverage reports
coverage/
*.coverage
*.coveragexml

# Temporary files
*.tmp
*.temp
"@

$gitignorePath = Join-Path $RootPath ".gitignore"
$gitignoreContent | Out-File -FilePath $gitignorePath -Encoding UTF8

# Create README.md
Write-Host "Creating README.md file..." -ForegroundColor Yellow
$readmeContent = @"
# 企业微信群转发助手

一个用于企业微信群消息智能转发的Windows桌面应用程序。

## 功能特性

- 🔍 **消息监控**: 实时监控指定企业微信群的消息
- 🎯 **订单号提取**: 自动识别和提取消息中的订单号
- 🔗 **API查询**: 根据订单号调用API获取转发配置
- 📤 **智能转发**: 自动将消息转发到对应的目标群组

## 技术栈

- **框架**: .NET 7.0 + WPF
- **架构**: 分层架构 + MVVM模式
- **UI**: Material Design
- **日志**: Serilog
- **HTTP**: HttpClient + Polly重试策略
- **测试**: xUnit + Moq + FluentAssertions

## 快速开始

### 环境要求

- Windows 10/11
- .NET 7.0 SDK
- Visual Studio 2022

### 构建和运行

1. 克隆项目
\`\`\`bash
git clone <repository-url>
cd 企业微信群转发助手
\`\`\`

2. 还原依赖
\`\`\`bash
dotnet restore
\`\`\`

3. 构建项目
\`\`\`bash
dotnet build
\`\`\`

4. 运行应用
\`\`\`bash
dotnet run --project src\\$ProjectName.WPF
\`\`\`

### 配置

1. 编辑 \`config/appsettings.json\` 配置企业微信API信息
2. 编辑 \`config/groups.json\` 配置群组和转发规则

## 项目结构

详见 [项目结构设计.md](项目结构设计.md)

## 开发计划

详见 [技术方案与开发计划.md](技术方案与开发计划.md)

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
"@

$readmePath = Join-Path $RootPath "README.md"
$readmeContent | Out-File -FilePath $readmePath -Encoding UTF8

Write-Host "Project initialization completed!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Open $ProjectName.sln with Visual Studio 2022" -ForegroundColor White
Write-Host "2. Configure WeChat API info in config/appsettings.json" -ForegroundColor White
Write-Host "3. Start developing core functionality modules" -ForegroundColor White
Write-Host "4. Run tests to ensure code quality" -ForegroundColor White
