# 企业微信群转发助手

一个用于企业微信群消息智能转发的Windows桌面应用程序。

## 功能特性

- 🔍 **消息监控**: 实时监控指定企业微信群的消息
- 🎯 **订单号提取**: 自动识别和提取消息中的订单号
- 🔗 **API查询**: 根据订单号调用API获取转发配置
- 📤 **智能转发**: 自动将消息转发到对应的目标群组
- 📊 **实时统计**: 显示消息处理统计和成功率
- 📝 **日志记录**: 完整的操作日志和错误追踪
- ⚙️ **配置管理**: 灵活的群组和规则配置

## 技术栈

- **框架**: .NET 7.0 + WPF
- **架构**: 分层架构 + MVVM模式
- **UI**: Material Design
- **日志**: Serilog
- **HTTP**: HttpClient + Polly重试策略
- **测试**: xUnit + Moq + FluentAssertions
- **依赖注入**: Microsoft.Extensions.DependencyInjection

## 项目结构

```
企业微信群转发助手/
├── src/                                    # 源代码
│   ├── WeChatForwarder.WPF/               # WPF主应用程序
│   ├── WeChatForwarder.Core/              # 核心业务逻辑
│   ├── WeChatForwarder.Infrastructure/    # 基础设施层
│   ├── WeChatForwarder.WeChat/            # 企业微信集成
│   └── WeChatForwarder.Common/            # 公共组件
├── tests/                                 # 测试项目
├── config/                                # 配置文件
├── docs/                                  # 文档
└── scripts/                               # 脚本文件
```

## 快速开始

### 环境要求

- Windows 10/11
- .NET 7.0 SDK 或 Visual Studio 2022
- 企业微信管理员权限（用于配置API）

### 安装和运行

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd 企业微信群转发助手
   ```

2. **打开项目**
   - 使用 Visual Studio 2022 打开 `WeChatForwarder.sln`
   - 或使用命令行：`dotnet build`

3. **配置企业微信API**
   - 编辑 `config/appsettings.json`
   - 填入企业微信的 CorpId、CorpSecret、AgentId 等信息

4. **配置群组和规则**
   - 编辑 `config/groups.json`
   - 配置源群组和转发规则

5. **运行应用**
   ```bash
   dotnet run --project src/WeChatForwarder.WPF
   ```

### 配置说明

#### 企业微信API配置 (config/appsettings.json)

```json
{
  "WeChat": {
    "CorpId": "你的企业ID",
    "CorpSecret": "你的应用密钥",
    "AgentId": "你的应用ID",
    "WebhookUrl": "http://localhost:8080/webhook"
  },
  "Api": {
    "BaseUrl": "你的API服务地址",
    "Timeout": 30,
    "RetryCount": 3
  }
}
```

#### 群组配置 (config/groups.json)

```json
{
  "Groups": [
    {
      "Id": "source_group",
      "Name": "源群组A",
      "ChatId": "企业微信群聊ID",
      "IsSource": true,
      "IsEnabled": true
    }
  ],
  "ForwardingRules": [
    {
      "Id": "rule_1",
      "Name": "订单转发规则",
      "SourceGroupId": "source_group",
      "OrderNumberPattern": "^ORD\\d{8}$",
      "ApiEndpoint": "/api/getTargetGroups",
      "IsEnabled": true
    }
  ]
}
```

## 开发指南

### 构建项目

```bash
# 还原依赖
dotnet restore

# 构建项目
dotnet build

# 运行测试
dotnet test

# 发布应用
dotnet publish src/WeChatForwarder.WPF -c Release -o publish
```

### 添加新功能

1. **添加新的服务接口**
   - 在 `WeChatForwarder.Core/Interfaces/` 中定义接口
   - 在 `WeChatForwarder.Core/Services/` 中实现服务

2. **添加新的UI页面**
   - 在 `WeChatForwarder.WPF/Views/` 中创建XAML文件
   - 在 `WeChatForwarder.WPF/ViewModels/` 中创建对应的ViewModel

3. **添加配置项**
   - 在相应的配置模型中添加属性
   - 更新配置文件模板

### 测试

项目包含完整的单元测试：

```bash
# 运行所有测试
dotnet test

# 运行特定测试类
dotnet test --filter "ClassName=OrderNumberExtractorTests"

# 生成测试覆盖率报告
dotnet test --collect:"XPlat Code Coverage"
```

## 核心功能说明

### 1. 消息监控

应用程序通过企业微信的Webhook机制接收群组消息：

- 实时接收指定群组的消息
- 支持多种消息类型（文本、图片、文件等）
- 自动去重和幂等性处理

### 2. 订单号提取

使用可配置的正则表达式规则提取订单号：

- 支持多种订单号格式
- 优先级排序的规则匹配
- 自定义提取规则

### 3. API查询

根据提取的订单号查询目标群组：

- HTTP客户端with重试机制
- 熔断器模式防止级联故障
- 缓存机制提升性能

### 4. 消息转发

智能转发消息到目标群组：

- 异步批量处理
- 失败重试机制
- 转发状态跟踪

## 故障排除

### 常见问题

1. **无法连接企业微信API**
   - 检查网络连接
   - 验证API配置信息
   - 查看日志文件中的详细错误信息

2. **订单号提取失败**
   - 检查正则表达式规则
   - 验证消息格式
   - 查看提取规则的优先级设置

3. **消息转发失败**
   - 检查目标群组配置
   - 验证API响应格式
   - 查看转发日志

### 日志文件

应用程序会在 `logs/` 目录下生成详细的日志文件：

- `app-{date}.log`: 应用程序运行日志
- 日志级别：Debug、Information、Warning、Error
- 自动按日期滚动，保留30天

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 支持

如果您遇到问题或有建议，请：

1. 查看 [FAQ](docs/FAQ.md)
2. 搜索现有的 [Issues](../../issues)
3. 创建新的 Issue 描述问题

## 更新日志

### v1.0.0 (开发中)

- ✅ 基础项目结构搭建
- ✅ 核心业务逻辑框架
- ✅ WPF用户界面基础
- ✅ 订单号提取功能
- 🚧 企业微信API集成
- 🚧 消息转发功能
- 🚧 配置管理界面
- 📋 性能优化和测试

---

**注意**: 本项目目前处于开发阶段，部分功能可能尚未完全实现。
