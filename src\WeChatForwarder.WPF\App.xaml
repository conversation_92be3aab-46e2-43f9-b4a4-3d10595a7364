<Application x:Class="WeChatForwarder.WPF.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- 自定义样式 -->
                <ResourceDictionary Source="Resources/Styles/ButtonStyles.xaml" />
                <ResourceDictionary Source="Resources/Styles/TextStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- 全局颜色定义 -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#FFC107"/>
            <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
            <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
            <SolidColorBrush x:Key="InfoBrush" Color="#2196F3"/>

            <!-- 全局字体大小 -->
            <system:Double x:Key="HeaderFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">24</system:Double>
            <system:Double x:Key="SubHeaderFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">18</system:Double>
            <system:Double x:Key="BodyFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">14</system:Double>
            <system:Double x:Key="CaptionFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">12</system:Double>

        </ResourceDictionary>
    </Application.Resources>
</Application>
