<Window x:Class="WeChatForwarder.WPF.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="企业微信群转发助手" 
        Height="800" 
        Width="1200"
        MinHeight="600"
        MinWidth="800"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <materialDesign:ColorZone Grid.Row="0" 
                                  Mode="PrimaryMid" 
                                  Padding="16"
                                  materialDesign:ElevationAssist.Elevation="Dp4">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 应用图标和标题 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="MessageProcessing" 
                                           Width="32" 
                                           Height="32" 
                                           VerticalAlignment="Center"
                                           Margin="0,0,12,0"/>
                    <TextBlock Text="企业微信群转发助手" 
                             FontSize="20" 
                             FontWeight="Medium"
                             VerticalAlignment="Center"/>
                </StackPanel>

                <!-- 连接状态 -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <materialDesign:PackIcon x:Name="StatusIcon"
                                           Kind="Circle" 
                                           Width="16" 
                                           Height="16" 
                                           VerticalAlignment="Center"
                                           Foreground="LightGreen"
                                           Margin="0,0,8,0"/>
                    <TextBlock x:Name="StatusText"
                             Text="已连接" 
                             VerticalAlignment="Center"
                             FontSize="14"/>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧导航 -->
            <materialDesign:ColorZone Grid.Column="0" 
                                      Mode="Light"
                                      Padding="0"
                                      materialDesign:ElevationAssist.Elevation="Dp2">
                <ListBox x:Name="NavigationListBox"
                         SelectionChanged="NavigationListBox_SelectionChanged"
                         SelectedIndex="0">
                    <ListBox.Resources>
                        <Style TargetType="ListBoxItem" BasedOn="{StaticResource MaterialDesignNavigationPrimaryListBoxItem}">
                            <Setter Property="Padding" Value="16,12"/>
                            <Setter Property="Height" Value="48"/>
                        </Style>
                    </ListBox.Resources>

                    <ListBoxItem>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ViewDashboard" Width="24" Height="24" Margin="0,0,16,0"/>
                            <TextBlock Text="监控面板" VerticalAlignment="Center"/>
                        </StackPanel>
                    </ListBoxItem>

                    <ListBoxItem>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Settings" Width="24" Height="24" Margin="0,0,16,0"/>
                            <TextBlock Text="配置管理" VerticalAlignment="Center"/>
                        </StackPanel>
                    </ListBoxItem>

                    <ListBoxItem>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FormatListBulleted" Width="24" Height="24" Margin="0,0,16,0"/>
                            <TextBlock Text="消息日志" VerticalAlignment="Center"/>
                        </StackPanel>
                    </ListBoxItem>

                    <ListBoxItem>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="ChartLine" Width="24" Height="24" Margin="0,0,16,0"/>
                            <TextBlock Text="统计报表" VerticalAlignment="Center"/>
                        </StackPanel>
                    </ListBoxItem>

                    <ListBoxItem>
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="Information" Width="24" Height="24" Margin="0,0,16,0"/>
                            <TextBlock Text="关于" VerticalAlignment="Center"/>
                        </StackPanel>
                    </ListBoxItem>
                </ListBox>
            </materialDesign:ColorZone>

            <!-- 右侧内容区域 -->
            <ContentControl Grid.Column="1" 
                          x:Name="MainContentControl"
                          Margin="16">
                <!-- 默认显示监控面板 -->
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 页面标题 -->
                    <TextBlock Grid.Row="0"
                             Text="监控面板"
                             FontSize="24"
                             FontWeight="Medium"
                             Margin="0,0,0,24"/>

                    <!-- 监控内容 -->
                    <Grid Grid.Row="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- 统计卡片 -->
                        <UniformGrid Grid.Row="0" Rows="1" Columns="4" Margin="0,0,0,24">
                            <!-- 今日消息数 -->
                            <materialDesign:Card Margin="0,0,8,0" Padding="16">
                                <StackPanel>
                                    <TextBlock Text="今日消息" FontSize="14" Foreground="Gray"/>
                                    <TextBlock Text="0" FontSize="32" FontWeight="Bold" Foreground="{StaticResource PrimaryBrush}"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- 成功转发数 -->
                            <materialDesign:Card Margin="8,0,8,0" Padding="16">
                                <StackPanel>
                                    <TextBlock Text="成功转发" FontSize="14" Foreground="Gray"/>
                                    <TextBlock Text="0" FontSize="32" FontWeight="Bold" Foreground="{StaticResource SuccessBrush}"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- 失败数 -->
                            <materialDesign:Card Margin="8,0,8,0" Padding="16">
                                <StackPanel>
                                    <TextBlock Text="处理失败" FontSize="14" Foreground="Gray"/>
                                    <TextBlock Text="0" FontSize="32" FontWeight="Bold" Foreground="{StaticResource ErrorBrush}"/>
                                </StackPanel>
                            </materialDesign:Card>

                            <!-- 成功率 -->
                            <materialDesign:Card Margin="8,0,0,0" Padding="16">
                                <StackPanel>
                                    <TextBlock Text="成功率" FontSize="14" Foreground="Gray"/>
                                    <TextBlock Text="100%" FontSize="32" FontWeight="Bold" Foreground="{StaticResource InfoBrush}"/>
                                </StackPanel>
                            </materialDesign:Card>
                        </UniformGrid>

                        <!-- 实时消息列表 -->
                        <materialDesign:Card Grid.Row="1">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0"
                                         Text="实时消息"
                                         FontSize="18"
                                         FontWeight="Medium"
                                         Margin="16,16,16,8"/>

                                <DataGrid Grid.Row="1"
                                        x:Name="MessagesDataGrid"
                                        AutoGenerateColumns="False"
                                        CanUserAddRows="False"
                                        CanUserDeleteRows="False"
                                        IsReadOnly="True"
                                        Margin="16,8,16,16">
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Header="时间" Binding="{Binding Timestamp, StringFormat=HH:mm:ss}" Width="80"/>
                                        <DataGridTextColumn Header="群组" Binding="{Binding GroupName}" Width="120"/>
                                        <DataGridTextColumn Header="发送者" Binding="{Binding SenderName}" Width="100"/>
                                        <DataGridTextColumn Header="消息内容" Binding="{Binding Content}" Width="*"/>
                                        <DataGridTextColumn Header="订单号" Binding="{Binding ExtractedOrderNumber}" Width="120"/>
                                        <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="80"/>
                                    </DataGrid.Columns>
                                </DataGrid>
                            </Grid>
                        </materialDesign:Card>
                    </Grid>
                </Grid>
            </ContentControl>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" 
                   Background="{DynamicResource MaterialDesignDivider}"
                   Height="24">
            <StatusBarItem>
                <TextBlock x:Name="StatusBarText" Text="就绪"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock x:Name="TimeText" Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat=yyyy-MM-dd HH:mm:ss}" 
                         xmlns:sys="clr-namespace:System;assembly=mscorlib"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
