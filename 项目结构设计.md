# 企业微信群转发助手 - 项目结构设计

## 推荐项目结构

```
企业微信群转发助手/
├── src/                                    # 源代码目录
│   ├── WeChatForwarder.WPF/               # WPF主应用程序
│   │   ├── Views/                         # 视图文件
│   │   │   ├── MainWindow.xaml
│   │   │   ├── ConfigWindow.xaml
│   │   │   ├── MonitorWindow.xaml
│   │   │   └── LogWindow.xaml
│   │   ├── ViewModels/                    # 视图模型
│   │   │   ├── MainViewModel.cs
│   │   │   ├── ConfigViewModel.cs
│   │   │   ├── MonitorViewModel.cs
│   │   │   └── LogViewModel.cs
│   │   ├── Controls/                      # 自定义控件
│   │   ├── Converters/                    # 值转换器
│   │   ├── Resources/                     # 资源文件
│   │   │   ├── Styles/
│   │   │   ├── Images/
│   │   │   └── Themes/
│   │   ├── App.xaml
│   │   ├── App.xaml.cs
│   │   └── WeChatForwarder.WPF.csproj
│   │
│   ├── WeChatForwarder.Core/              # 核心业务逻辑
│   │   ├── Services/                      # 服务层
│   │   │   ├── IWeChatService.cs
│   │   │   ├── WeChatService.cs
│   │   │   ├── IMessageProcessor.cs
│   │   │   ├── MessageProcessor.cs
│   │   │   ├── IOrderNumberExtractor.cs
│   │   │   ├── OrderNumberExtractor.cs
│   │   │   ├── IApiService.cs
│   │   │   ├── ApiService.cs
│   │   │   ├── IForwardingService.cs
│   │   │   └── ForwardingService.cs
│   │   ├── Models/                        # 数据模型
│   │   │   ├── Message.cs
│   │   │   ├── GroupConfig.cs
│   │   │   ├── ApiConfig.cs
│   │   │   ├── ForwardingRule.cs
│   │   │   └── OrderInfo.cs
│   │   ├── Interfaces/                    # 接口定义
│   │   ├── Exceptions/                    # 自定义异常
│   │   ├── Extensions/                    # 扩展方法
│   │   └── WeChatForwarder.Core.csproj
│   │
│   ├── WeChatForwarder.Infrastructure/    # 基础设施层
│   │   ├── Data/                          # 数据访问
│   │   │   ├── IRepository.cs
│   │   │   ├── JsonRepository.cs
│   │   │   ├── SqliteRepository.cs
│   │   │   └── ConfigurationManager.cs
│   │   ├── Logging/                       # 日志记录
│   │   │   ├── ILogger.cs
│   │   │   ├── FileLogger.cs
│   │   │   └── DatabaseLogger.cs
│   │   ├── Http/                          # HTTP客户端
│   │   │   ├── IHttpClientFactory.cs
│   │   │   ├── HttpClientFactory.cs
│   │   │   └── RetryPolicy.cs
│   │   ├── Cache/                         # 缓存管理
│   │   │   ├── ICache.cs
│   │   │   └── MemoryCache.cs
│   │   └── WeChatForwarder.Infrastructure.csproj
│   │
│   ├── WeChatForwarder.WeChat/            # 企业微信集成
│   │   ├── Api/                           # API封装
│   │   │   ├── IWeChatApiClient.cs
│   │   │   ├── WeChatApiClient.cs
│   │   │   └── ApiModels/
│   │   ├── Webhook/                       # Webhook处理
│   │   │   ├── IWebhookHandler.cs
│   │   │   ├── WebhookHandler.cs
│   │   │   └── WebhookServer.cs
│   │   ├── Authentication/                # 身份验证
│   │   │   ├── IAuthenticationService.cs
│   │   │   └── AuthenticationService.cs
│   │   └── WeChatForwarder.WeChat.csproj
│   │
│   └── WeChatForwarder.Common/            # 公共组件
│       ├── Constants/                     # 常量定义
│       ├── Helpers/                       # 帮助类
│       ├── Utilities/                     # 工具类
│       ├── Configuration/                 # 配置相关
│       └── WeChatForwarder.Common.csproj
│
├── tests/                                 # 测试项目
│   ├── WeChatForwarder.Core.Tests/
│   ├── WeChatForwarder.Infrastructure.Tests/
│   ├── WeChatForwarder.WeChat.Tests/
│   └── WeChatForwarder.Integration.Tests/
│
├── docs/                                  # 文档目录
│   ├── api/                              # API文档
│   ├── user-guide/                       # 用户指南
│   ├── deployment/                       # 部署文档
│   └── architecture/                     # 架构文档
│
├── scripts/                              # 脚本文件
│   ├── build.ps1                        # 构建脚本
│   ├── deploy.ps1                       # 部署脚本
│   └── setup-dev.ps1                    # 开发环境设置
│
├── config/                               # 配置文件
│   ├── appsettings.json                 # 应用配置
│   ├── logging.json                     # 日志配置
│   └── groups.json                      # 群组配置
│
├── assets/                               # 资源文件
│   ├── icons/                           # 图标
│   ├── images/                          # 图片
│   └── templates/                       # 模板文件
│
├── .gitignore
├── README.md
├── WeChatForwarder.sln                  # 解决方案文件
└── Directory.Build.props                # 全局构建属性
```

## 核心项目说明

### 1. WeChatForwarder.WPF
- **职责**：用户界面层，负责用户交互和界面展示
- **技术**：WPF + MVVM模式
- **依赖**：Core、Infrastructure、Common

### 2. WeChatForwarder.Core
- **职责**：核心业务逻辑，包含所有业务规则和流程
- **技术**：.NET Core类库
- **依赖**：Common

### 3. WeChatForwarder.Infrastructure
- **职责**：基础设施服务，如数据访问、日志、缓存等
- **技术**：.NET Core类库
- **依赖**：Core、Common

### 4. WeChatForwarder.WeChat
- **职责**：企业微信API集成和相关功能
- **技术**：.NET Core类库
- **依赖**：Core、Infrastructure、Common

### 5. WeChatForwarder.Common
- **职责**：公共组件和工具类
- **技术**：.NET Core类库
- **依赖**：无

## 关键设计模式

### 1. MVVM模式
- **View**：XAML界面文件
- **ViewModel**：界面逻辑和数据绑定
- **Model**：业务数据模型

### 2. 依赖注入
- 使用Microsoft.Extensions.DependencyInjection
- 接口驱动设计，便于测试和扩展

### 3. 仓储模式
- 抽象数据访问层
- 支持多种存储方式（JSON、SQLite等）

### 4. 策略模式
- 订单号提取策略
- 消息转发策略
- API调用策略

### 5. 观察者模式
- 消息监控和事件通知
- 状态变更通知

## 技术选型详细说明

### UI框架：WPF
```xml
<!-- 主要NuGet包 -->
<PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.1" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.0" />
<PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
```

### HTTP客户端
```xml
<PackageReference Include="Microsoft.Extensions.Http" Version="7.0.0" />
<PackageReference Include="Polly" Version="7.2.4" />
<PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
```

### 日志框架
```xml
<PackageReference Include="Serilog" Version="3.0.1" />
<PackageReference Include="Serilog.Extensions.Hosting" Version="7.0.0" />
<PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
<PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
```

### 数据存储
```xml
<PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="7.0.10" />
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
```

### 测试框架
```xml
<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.7.1" />
<PackageReference Include="xunit" Version="2.4.2" />
<PackageReference Include="xunit.runner.visualstudio" Version="2.4.5" />
<PackageReference Include="Moq" Version="4.20.69" />
<PackageReference Include="FluentAssertions" Version="6.12.0" />
```

## 配置管理

### appsettings.json
```json
{
  "WeChat": {
    "CorpId": "",
    "CorpSecret": "",
    "AgentId": "",
    "WebhookUrl": "http://localhost:8080/webhook"
  },
  "Api": {
    "BaseUrl": "",
    "Timeout": 30,
    "RetryCount": 3
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning"
    },
    "File": {
      "Path": "logs/app-.log",
      "RollingInterval": "Day",
      "RetainedFileCountLimit": 30
    }
  }
}
```

## 开发环境要求

### 必需软件
- Visual Studio 2022 (Community版本即可)
- .NET 7.0 SDK
- Git for Windows

### 推荐工具
- Postman (API测试)
- DB Browser for SQLite (数据库查看)
- Windows Terminal (命令行工具)

## 下一步行动

1. **创建解决方案结构**
2. **配置项目依赖关系**
3. **实现基础架构代码**
4. **设置开发环境**
5. **开始核心功能开发**

这个项目结构设计遵循了清晰的分层架构原则，便于维护和扩展。每个项目都有明确的职责边界，使用依赖注入实现松耦合设计。
