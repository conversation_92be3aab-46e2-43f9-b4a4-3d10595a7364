using Microsoft.Extensions.Logging;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using WeChatForwarder.WPF.ViewModels;

namespace WeChatForwarder.WPF
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly ILogger<MainWindow> _logger;
        private readonly MainViewModel _viewModel;
        private readonly DispatcherTimer _timer;

        public MainWindow(ILogger<MainWindow> logger, MainViewModel viewModel)
        {
            InitializeComponent();
            
            _logger = logger;
            _viewModel = viewModel;
            DataContext = _viewModel;

            // 初始化定时器更新时间显示
            _timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(1)
            };
            _timer.Tick += Timer_Tick;
            _timer.Start();

            _logger.LogInformation("主窗口初始化完成");
        }

        private void Timer_Tick(object? sender, EventArgs e)
        {
            TimeText.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        private void NavigationListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (NavigationListBox.SelectedIndex < 0) return;

            try
            {
                switch (NavigationListBox.SelectedIndex)
                {
                    case 0: // 监控面板
                        ShowMonitoringPanel();
                        break;
                    case 1: // 配置管理
                        ShowConfigurationPanel();
                        break;
                    case 2: // 消息日志
                        ShowMessageLogPanel();
                        break;
                    case 3: // 统计报表
                        ShowStatisticsPanel();
                        break;
                    case 4: // 关于
                        ShowAboutPanel();
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换导航页面时发生错误");
                MessageBox.Show($"切换页面失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowMonitoringPanel()
        {
            // 显示监控面板（当前默认内容）
            StatusBarText.Text = "监控面板";
            _logger.LogDebug("切换到监控面板");
        }

        private void ShowConfigurationPanel()
        {
            // TODO: 创建配置管理用户控件
            var configPanel = new Grid();
            configPanel.Children.Add(new TextBlock 
            { 
                Text = "配置管理功能开发中...", 
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = 18
            });
            
            MainContentControl.Content = configPanel;
            StatusBarText.Text = "配置管理";
            _logger.LogDebug("切换到配置管理");
        }

        private void ShowMessageLogPanel()
        {
            // TODO: 创建消息日志用户控件
            var logPanel = new Grid();
            logPanel.Children.Add(new TextBlock 
            { 
                Text = "消息日志功能开发中...", 
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = 18
            });
            
            MainContentControl.Content = logPanel;
            StatusBarText.Text = "消息日志";
            _logger.LogDebug("切换到消息日志");
        }

        private void ShowStatisticsPanel()
        {
            // TODO: 创建统计报表用户控件
            var statsPanel = new Grid();
            statsPanel.Children.Add(new TextBlock 
            { 
                Text = "统计报表功能开发中...", 
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = 18
            });
            
            MainContentControl.Content = statsPanel;
            StatusBarText.Text = "统计报表";
            _logger.LogDebug("切换到统计报表");
        }

        private void ShowAboutPanel()
        {
            var aboutPanel = new Grid();
            var stackPanel = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            stackPanel.Children.Add(new TextBlock 
            { 
                Text = "企业微信群转发助手", 
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 16)
            });

            stackPanel.Children.Add(new TextBlock 
            { 
                Text = "版本：1.0.0", 
                FontSize = 16,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 8)
            });

            stackPanel.Children.Add(new TextBlock 
            { 
                Text = "一个用于企业微信群消息智能转发的桌面应用程序", 
                FontSize = 14,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 16)
            });

            stackPanel.Children.Add(new TextBlock 
            { 
                Text = "功能特性：", 
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 8)
            });

            var features = new string[]
            {
                "• 实时监控企业微信群消息",
                "• 智能提取订单号信息", 
                "• API查询目标群组映射",
                "• 自动转发到指定群组",
                "• 完整的日志记录和统计"
            };

            foreach (var feature in features)
            {
                stackPanel.Children.Add(new TextBlock 
                { 
                    Text = feature, 
                    FontSize = 12,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 2, 0, 2)
                });
            }

            aboutPanel.Children.Add(stackPanel);
            MainContentControl.Content = aboutPanel;
            StatusBarText.Text = "关于";
            _logger.LogDebug("切换到关于页面");
        }

        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            _logger.LogInformation("主窗口已关闭");
            base.OnClosed(e);
        }
    }
}
