using WeChatForwarder.Core.Models;

namespace WeChatForwarder.Core.Interfaces
{
    /// <summary>
    /// 消息处理器接口
    /// </summary>
    public interface IMessageProcessor
    {
        /// <summary>
        /// 处理消息
        /// </summary>
        /// <param name="message">待处理的消息</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理结果</returns>
        Task<MessageProcessResult> ProcessMessageAsync(Message message, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量处理消息
        /// </summary>
        /// <param name="messages">待处理的消息列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>处理结果列表</returns>
        Task<IEnumerable<MessageProcessResult>> ProcessMessagesAsync(IEnumerable<Message> messages, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 消息处理结果
    /// </summary>
    public class MessageProcessResult
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public string MessageId { get; set; } = string.Empty;

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 处理状态
        /// </summary>
        public MessageProcessStatus Status { get; set; }

        /// <summary>
        /// 提取的订单号
        /// </summary>
        public string? ExtractedOrderNumber { get; set; }

        /// <summary>
        /// 目标群组ID列表
        /// </summary>
        public List<string> TargetGroupIds { get; set; } = new();

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 处理时间
        /// </summary>
        public DateTime ProcessedAt { get; set; } = DateTime.Now;
    }
}
