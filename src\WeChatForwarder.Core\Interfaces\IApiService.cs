using WeChatForwarder.Core.Models;

namespace WeChatForwarder.Core.Interfaces
{
    /// <summary>
    /// API服务接口
    /// </summary>
    public interface IApiService
    {
        /// <summary>
        /// 根据订单号查询目标群组
        /// </summary>
        /// <param name="orderNumber">订单号</param>
        /// <param name="sourceGroupId">源群组ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>查询结果</returns>
        Task<ApiQueryResult> QueryTargetGroupsAsync(string orderNumber, string sourceGroupId, CancellationToken cancellationToken = default);

        /// <summary>
        /// 测试API连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>连接是否成功</returns>
        Task<bool> TestConnectionAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取API健康状态
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>健康状态信息</returns>
        Task<ApiHealthStatus> GetHealthStatusAsync(CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// API健康状态
    /// </summary>
    public class ApiHealthStatus
    {
        /// <summary>
        /// 是否健康
        /// </summary>
        public bool IsHealthy { get; set; }

        /// <summary>
        /// 响应时间（毫秒）
        /// </summary>
        public long ResponseTimeMs { get; set; }

        /// <summary>
        /// 状态消息
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// 检查时间
        /// </summary>
        public DateTime CheckedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 额外信息
        /// </summary>
        public Dictionary<string, object> AdditionalInfo { get; set; } = new();
    }
}
