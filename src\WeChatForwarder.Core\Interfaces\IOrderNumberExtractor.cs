using WeChatForwarder.Core.Models;

namespace WeChatForwarder.Core.Interfaces
{
    /// <summary>
    /// 订单号提取器接口
    /// </summary>
    public interface IOrderNumberExtractor
    {
        /// <summary>
        /// 从消息中提取订单号
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="groupId">群组ID</param>
        /// <returns>提取的订单号，如果没有找到则返回null</returns>
        Task<string?> ExtractOrderNumberAsync(string message, string groupId);

        /// <summary>
        /// 验证订单号格式
        /// </summary>
        /// <param name="orderNumber">订单号</param>
        /// <param name="pattern">验证模式</param>
        /// <returns>是否有效</returns>
        bool ValidateOrderNumber(string orderNumber, string pattern);

        /// <summary>
        /// 获取适用的提取规则
        /// </summary>
        /// <param name="groupId">群组ID</param>
        /// <returns>提取规则列表</returns>
        Task<IEnumerable<OrderExtractionRule>> GetExtractionRulesAsync(string groupId);
    }

    /// <summary>
    /// 订单号提取规则
    /// </summary>
    public class OrderExtractionRule
    {
        /// <summary>
        /// 规则ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 规则名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 正则表达式模式
        /// </summary>
        public string Pattern { get; set; } = string.Empty;

        /// <summary>
        /// 适用的群组ID列表
        /// </summary>
        public List<string> ApplicableGroupIds { get; set; } = new();

        /// <summary>
        /// 优先级（数字越小优先级越高）
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 规则描述
        /// </summary>
        public string? Description { get; set; }
    }
}
