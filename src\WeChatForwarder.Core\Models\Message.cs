using System;

namespace WeChatForwarder.Core.Models
{
    /// <summary>
    /// 企业微信消息模型
    /// </summary>
    public class Message
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 消息内容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 消息类型
        /// </summary>
        public MessageType Type { get; set; }

        /// <summary>
        /// 发送者ID
        /// </summary>
        public string SenderId { get; set; } = string.Empty;

        /// <summary>
        /// 发送者姓名
        /// </summary>
        public string SenderName { get; set; } = string.Empty;

        /// <summary>
        /// 群组ID
        /// </summary>
        public string GroupId { get; set; } = string.Empty;

        /// <summary>
        /// 群组名称
        /// </summary>
        public string GroupName { get; set; } = string.Empty;

        /// <summary>
        /// 消息时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 原始消息数据
        /// </summary>
        public string RawData { get; set; } = string.Empty;

        /// <summary>
        /// 提取的订单号
        /// </summary>
        public string? ExtractedOrderNumber { get; set; }

        /// <summary>
        /// 处理状态
        /// </summary>
        public MessageProcessStatus Status { get; set; } = MessageProcessStatus.Pending;

        /// <summary>
        /// 错误信息
        /// </summary>
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// 消息类型枚举
    /// </summary>
    public enum MessageType
    {
        Text = 1,
        Image = 2,
        File = 3,
        Link = 4,
        Other = 99
    }

    /// <summary>
    /// 消息处理状态
    /// </summary>
    public enum MessageProcessStatus
    {
        /// <summary>
        /// 待处理
        /// </summary>
        Pending = 0,

        /// <summary>
        /// 处理中
        /// </summary>
        Processing = 1,

        /// <summary>
        /// 已转发
        /// </summary>
        Forwarded = 2,

        /// <summary>
        /// 无需转发
        /// </summary>
        Ignored = 3,

        /// <summary>
        /// 处理失败
        /// </summary>
        Failed = 4
    }
}
