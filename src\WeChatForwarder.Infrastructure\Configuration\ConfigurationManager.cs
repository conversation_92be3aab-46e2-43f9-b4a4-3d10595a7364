using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeChatForwarder.Core.Models;

namespace WeChatForwarder.Infrastructure.Configuration
{
    /// <summary>
    /// 配置管理器
    /// </summary>
    public class ConfigurationManager
    {
        private readonly ILogger<ConfigurationManager> _logger;
        private readonly string _configDirectory;

        public ConfigurationManager(ILogger<ConfigurationManager> logger, string? configDirectory = null)
        {
            _logger = logger;
            _configDirectory = configDirectory ?? Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "config");
            
            // 确保配置目录存在
            if (!Directory.Exists(_configDirectory))
            {
                Directory.CreateDirectory(_configDirectory);
            }
        }

        /// <summary>
        /// 加载应用程序设置
        /// </summary>
        public async Task<AppSettings> LoadAppSettingsAsync()
        {
            var filePath = Path.Combine(_configDirectory, "appsettings.json");
            
            try
            {
                if (!File.Exists(filePath))
                {
                    _logger.LogWarning("配置文件不存在，创建默认配置: {FilePath}", filePath);
                    var defaultSettings = CreateDefaultAppSettings();
                    await SaveAppSettingsAsync(defaultSettings);
                    return defaultSettings;
                }

                var json = await File.ReadAllTextAsync(filePath);
                var settings = JsonConvert.DeserializeObject<AppSettings>(json);
                
                if (settings == null)
                {
                    _logger.LogWarning("配置文件格式错误，使用默认配置");
                    return CreateDefaultAppSettings();
                }

                _logger.LogInformation("成功加载应用程序配置");
                return settings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载应用程序配置失败: {FilePath}", filePath);
                return CreateDefaultAppSettings();
            }
        }

        /// <summary>
        /// 保存应用程序设置
        /// </summary>
        public async Task SaveAppSettingsAsync(AppSettings settings)
        {
            var filePath = Path.Combine(_configDirectory, "appsettings.json");
            
            try
            {
                var json = JsonConvert.SerializeObject(settings, Formatting.Indented);
                await File.WriteAllTextAsync(filePath, json);
                _logger.LogInformation("应用程序配置保存成功: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存应用程序配置失败: {FilePath}", filePath);
                throw;
            }
        }

        /// <summary>
        /// 加载群组配置
        /// </summary>
        public async Task<GroupConfiguration> LoadGroupConfigurationAsync()
        {
            var filePath = Path.Combine(_configDirectory, "groups.json");
            
            try
            {
                if (!File.Exists(filePath))
                {
                    _logger.LogWarning("群组配置文件不存在，创建默认配置: {FilePath}", filePath);
                    var defaultConfig = CreateDefaultGroupConfiguration();
                    await SaveGroupConfigurationAsync(defaultConfig);
                    return defaultConfig;
                }

                var json = await File.ReadAllTextAsync(filePath);
                var config = JsonConvert.DeserializeObject<GroupConfiguration>(json);
                
                if (config == null)
                {
                    _logger.LogWarning("群组配置文件格式错误，使用默认配置");
                    return CreateDefaultGroupConfiguration();
                }

                _logger.LogInformation("成功加载群组配置，群组数: {GroupCount}, 规则数: {RuleCount}", 
                    config.Groups.Count, config.ForwardingRules.Count);
                return config;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加载群组配置失败: {FilePath}", filePath);
                return CreateDefaultGroupConfiguration();
            }
        }

        /// <summary>
        /// 保存群组配置
        /// </summary>
        public async Task SaveGroupConfigurationAsync(GroupConfiguration configuration)
        {
            var filePath = Path.Combine(_configDirectory, "groups.json");
            
            try
            {
                var json = JsonConvert.SerializeObject(configuration, Formatting.Indented);
                await File.WriteAllTextAsync(filePath, json);
                _logger.LogInformation("群组配置保存成功: {FilePath}", filePath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "保存群组配置失败: {FilePath}", filePath);
                throw;
            }
        }

        /// <summary>
        /// 创建默认应用程序设置
        /// </summary>
        private static AppSettings CreateDefaultAppSettings()
        {
            return new AppSettings
            {
                WeChat = new WeChatConfig
                {
                    CorpId = "",
                    CorpSecret = "",
                    AgentId = "",
                    WebhookUrl = "http://localhost:8080/webhook"
                },
                Api = new ApiConfig
                {
                    BaseUrl = "",
                    Timeout = 30,
                    RetryCount = 3
                }
            };
        }

        /// <summary>
        /// 创建默认群组配置
        /// </summary>
        private static GroupConfiguration CreateDefaultGroupConfiguration()
        {
            return new GroupConfiguration
            {
                Groups = new List<GroupConfig>
                {
                    new GroupConfig
                    {
                        Id = "source_group",
                        Name = "源群组A",
                        ChatId = "",
                        IsSource = true,
                        Description = "监控的源群组"
                    }
                },
                ForwardingRules = new List<ForwardingRule>
                {
                    new ForwardingRule
                    {
                        Id = "default_rule",
                        Name = "默认转发规则",
                        SourceGroupId = "source_group",
                        OrderNumberPattern = @"\b\d{10,20}\b",
                        ApiEndpoint = "/api/getTargetGroups",
                        Description = "默认的订单号匹配和转发规则"
                    }
                }
            };
        }
    }

    /// <summary>
    /// 应用程序设置
    /// </summary>
    public class AppSettings
    {
        /// <summary>
        /// 企业微信配置
        /// </summary>
        public WeChatConfig WeChat { get; set; } = new();

        /// <summary>
        /// API配置
        /// </summary>
        public ApiConfig Api { get; set; } = new();

        /// <summary>
        /// 日志配置
        /// </summary>
        public LoggingConfig Logging { get; set; } = new();
    }

    /// <summary>
    /// 日志配置
    /// </summary>
    public class LoggingConfig
    {
        /// <summary>
        /// 日志级别
        /// </summary>
        public string LogLevel { get; set; } = "Information";

        /// <summary>
        /// 日志文件路径
        /// </summary>
        public string FilePath { get; set; } = "logs/app-.log";

        /// <summary>
        /// 日志文件保留天数
        /// </summary>
        public int RetainedFileCountLimit { get; set; } = 30;
    }
}
