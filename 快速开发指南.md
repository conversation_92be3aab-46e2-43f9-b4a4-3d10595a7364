# 企业微信群转发助手 - 快速开发指南

## 🚀 基于文件监控的简化方案

### 为什么选择文件监控方案？

✅ **开发简单**：无需企业微信API权限，避免复杂的身份验证  
✅ **稳定可靠**：直接读取本地文件，不受网络影响  
✅ **实时性好**：文件变化即时响应，延迟极低  
✅ **部署容易**：无需管理员权限，用户自主部署  
✅ **维护成本低**：不依赖外部API，减少故障点  

## 📋 开发步骤

### 第一步：项目初始化 (10分钟)

1. **运行初始化脚本**
```powershell
# 在项目根目录执行
.\setup-project.ps1
```

2. **打开项目**
```bash
# 使用Visual Studio 2022打开
start WeChatForwarder.sln
```

3. **验证项目结构**
确认以下项目已创建：
- WeChatForwarder.WPF (主应用)
- WeChatForwarder.Core (业务逻辑)
- WeChatForwarder.FileMonitor (文件监控)
- WeChatForwarder.Infrastructure (基础设施)
- WeChatForwarder.Common (公共组件)

### 第二步：核心功能开发 (2-3天)

#### 2.1 企业微信路径检测 (半天)

创建 `WeChatPathDetector.cs`：
```csharp
public class WeChatPathDetector
{
    public string DetectWeChatDataPath()
    {
        // 检测企业微信数据目录
        var possiblePaths = new[]
        {
            Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "WeChat Files"),
            Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Tencent", "WeChat")
        };
        
        foreach (var basePath in possiblePaths)
        {
            if (Directory.Exists(basePath))
            {
                var msgDirs = Directory.GetDirectories(basePath)
                    .Where(d => Directory.Exists(Path.Combine(d, "Msg")))
                    .ToList();
                    
                if (msgDirs.Any())
                    return Path.Combine(msgDirs.First(), "Msg");
            }
        }
        
        throw new DirectoryNotFoundException("未找到企业微信数据目录");
    }
}
```

#### 2.2 数据库操作封装 (1天)

创建 `WeChatDatabaseManager.cs`：
```csharp
public class WeChatDatabaseManager
{
    private readonly string _dbPath;
    
    public WeChatDatabaseManager(string dbPath)
    {
        _dbPath = dbPath;
    }
    
    public List<Message> ReadNewMessages(long fromId)
    {
        using var connection = new SQLiteConnection($"Data Source={_dbPath};Version=3;Read Only=True;");
        connection.Open();
        
        var command = new SQLiteCommand(@"
            SELECT localId, StrTalker, StrContent, CreateTime, IsSender 
            FROM MSG 
            WHERE localId > @fromId AND Type = 1 AND IsSender = 0
            ORDER BY localId", connection);
            
        command.Parameters.AddWithValue("@fromId", fromId);
        
        var messages = new List<Message>();
        using var reader = command.ExecuteReader();
        
        while (reader.Read())
        {
            messages.Add(new Message
            {
                Id = reader.GetInt64("localId"),
                GroupId = reader.GetString("StrTalker"),
                Content = reader.GetString("StrContent"),
                CreateTime = DateTimeOffset.FromUnixTimeSeconds(reader.GetInt64("CreateTime")),
                IsSender = reader.GetInt32("IsSender") == 1
            });
        }
        
        return messages;
    }
}
```

#### 2.3 文件监控服务 (1天)

创建 `FileMonitorService.cs`：
```csharp
public class FileMonitorService
{
    private FileSystemWatcher _watcher;
    private WeChatDatabaseManager _dbManager;
    private long _lastProcessedId = 0;
    
    public event Action<Message> NewMessageReceived;
    
    public void StartMonitoring(string dbPath)
    {
        _dbManager = new WeChatDatabaseManager(dbPath);
        
        _watcher = new FileSystemWatcher
        {
            Path = Path.GetDirectoryName(dbPath),
            Filter = Path.GetFileName(dbPath),
            NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size,
            EnableRaisingEvents = true
        };
        
        _watcher.Changed += OnFileChanged;
        
        // 读取初始状态
        LoadLastProcessedId();
    }
    
    private void OnFileChanged(object sender, FileSystemEventArgs e)
    {
        try
        {
            // 延迟一下，等待文件写入完成
            Thread.Sleep(100);
            
            var newMessages = _dbManager.ReadNewMessages(_lastProcessedId);
            
            foreach (var message in newMessages)
            {
                NewMessageReceived?.Invoke(message);
                _lastProcessedId = message.Id;
            }
            
            SaveLastProcessedId();
        }
        catch (Exception ex)
        {
            // 记录错误日志
            Console.WriteLine($"处理文件变化时出错: {ex.Message}");
        }
    }
}
```

#### 2.4 消息发送实现 (半天)

创建 `ClipboardMessageSender.cs`：
```csharp
public class ClipboardMessageSender
{
    public async Task<bool> SendMessage(string groupName, string content)
    {
        try
        {
            // 1. 激活企业微信窗口
            var wechatProcess = Process.GetProcessesByName("WXWork").FirstOrDefault();
            if (wechatProcess == null) return false;
            
            // 2. 将内容复制到剪贴板
            Clipboard.SetText(content);
            
            // 3. 发送快捷键 (这里需要用户手动切换到目标群)
            // 实际实现中可以添加更智能的群组切换逻辑
            SendKeys.SendWait("^v");  // Ctrl+V 粘贴
            await Task.Delay(100);
            SendKeys.SendWait("{ENTER}");  // Enter 发送
            
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"发送消息失败: {ex.Message}");
            return false;
        }
    }
}
```

### 第三步：业务逻辑集成 (1天)

#### 3.1 订单号识别

创建 `OrderNumberExtractor.cs`：
```csharp
public class OrderNumberExtractor
{
    private readonly List<string> _patterns;
    
    public OrderNumberExtractor()
    {
        _patterns = new List<string>
        {
            @"^ORD\d{8}$",      // ORD12345678
            @"^\d{10,}$",       // 10位以上数字
            @"^[A-Z]{2}\d{6}$"  // AB123456
        };
    }
    
    public string ExtractOrderNumber(string content)
    {
        foreach (var pattern in _patterns)
        {
            var match = Regex.Match(content, pattern);
            if (match.Success)
                return match.Value;
        }
        
        return null;
    }
}
```

#### 3.2 主要业务流程

创建 `MessageProcessor.cs`：
```csharp
public class MessageProcessor
{
    private readonly OrderNumberExtractor _extractor;
    private readonly ClipboardMessageSender _sender;
    private readonly HttpClient _httpClient;
    
    public MessageProcessor()
    {
        _extractor = new OrderNumberExtractor();
        _sender = new ClipboardMessageSender();
        _httpClient = new HttpClient();
    }
    
    public async Task ProcessMessage(Message message)
    {
        // 1. 提取订单号
        var orderNumber = _extractor.ExtractOrderNumber(message.Content);
        if (string.IsNullOrEmpty(orderNumber))
            return;
            
        // 2. 调用API查询目标群组
        var targetGroup = await GetTargetGroup(orderNumber);
        if (string.IsNullOrEmpty(targetGroup))
            return;
            
        // 3. 转发消息
        await _sender.SendMessage(targetGroup, message.Content);
        
        // 4. 记录日志
        Console.WriteLine($"消息已转发: {orderNumber} -> {targetGroup}");
    }
    
    private async Task<string> GetTargetGroup(string orderNumber)
    {
        try
        {
            var response = await _httpClient.GetStringAsync($"https://api.example.com/getTargetGroup?orderNumber={orderNumber}");
            var result = JsonConvert.DeserializeObject<dynamic>(response);
            return result.targetGroup;
        }
        catch
        {
            return null;
        }
    }
}
```

### 第四步：用户界面开发 (1天)

#### 4.1 主窗口设计

创建简单的WPF界面：
```xml
<Window x:Class="WeChatForwarder.WPF.MainWindow">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 控制面板 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10">
            <Button Name="StartButton" Content="开始监控" Click="StartButton_Click"/>
            <Button Name="StopButton" Content="停止监控" Click="StopButton_Click"/>
            <Button Name="ConfigButton" Content="配置" Click="ConfigButton_Click"/>
        </StackPanel>
        
        <!-- 日志显示 -->
        <TextBox Grid.Row="1" Name="LogTextBox" IsReadOnly="True" 
                 VerticalScrollBarVisibility="Auto" Margin="10"/>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem Name="StatusLabel" Content="就绪"/>
        </StatusBar>
    </Grid>
</Window>
```

#### 4.2 主窗口逻辑

```csharp
public partial class MainWindow : Window
{
    private FileMonitorService _monitorService;
    private MessageProcessor _processor;
    
    public MainWindow()
    {
        InitializeComponent();
        _monitorService = new FileMonitorService();
        _processor = new MessageProcessor();
        
        _monitorService.NewMessageReceived += OnNewMessage;
    }
    
    private void StartButton_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var detector = new WeChatPathDetector();
            var dbPath = Path.Combine(detector.DetectWeChatDataPath(), "MSG0.db");
            
            _monitorService.StartMonitoring(dbPath);
            
            LogTextBox.AppendText($"开始监控: {dbPath}\n");
            StatusLabel.Content = "监控中...";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"启动失败: {ex.Message}");
        }
    }
    
    private async void OnNewMessage(Message message)
    {
        Dispatcher.Invoke(() =>
        {
            LogTextBox.AppendText($"收到消息: {message.Content}\n");
        });
        
        await _processor.ProcessMessage(message);
    }
}
```

## 🎯 快速测试

### 1. 本地测试步骤

1. **启动应用程序**
2. **点击"开始监控"**
3. **在企业微信中发送包含订单号的测试消息**
4. **观察应用程序日志输出**

### 2. 调试技巧

- 使用 DB Browser for SQLite 查看企业微信数据库结构
- 在 `OnFileChanged` 方法中添加断点调试
- 检查企业微信数据目录权限

## ⚠️ 注意事项

1. **文件权限**：确保应用有读取企业微信数据目录的权限
2. **文件锁定**：企业微信运行时数据库可能被锁定，使用只读模式打开
3. **版本兼容**：不同版本的企业微信数据库结构可能有差异
4. **消息发送**：当前实现需要用户手动切换到目标群组

## 🔧 常见问题

**Q: 找不到企业微信数据目录？**  
A: 检查企业微信安装路径，或手动指定数据目录

**Q: 数据库读取失败？**  
A: 确保企业微信已登录且有聊天记录

**Q: 消息发送不成功？**  
A: 检查企业微信窗口是否激活，目标群组是否正确

## 📈 后续优化

1. **智能群组切换**：实现自动切换到目标群组
2. **消息模板**：支持自定义转发消息格式
3. **批量处理**：支持批量处理历史消息
4. **错误恢复**：完善错误处理和重试机制

这个方案大大简化了开发复杂度，让您可以在1-2天内完成核心功能的开发！
