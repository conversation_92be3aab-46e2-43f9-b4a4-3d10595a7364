# WeChat Forwarder Project Initialization Script
param(
    [string]$ProjectName = "WeChatForwarder",
    [string]$RootPath = "."
)

Write-Host "Starting WeChat Forwarder project creation..." -ForegroundColor Green

# Create directory structure
$directories = @(
    "src",
    "src\$ProjectName.WPF",
    "src\$ProjectName.Core", 
    "src\$ProjectName.Infrastructure",
    "src\$ProjectName.WeChat",
    "src\$ProjectName.Common",
    "tests",
    "config",
    "docs"
)

Write-Host "Creating directories..." -ForegroundColor Yellow
foreach ($dir in $directories) {
    $fullPath = Join-Path $RootPath $dir
    if (!(Test-Path $fullPath)) {
        New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
        Write-Host "  Created: $dir" -ForegroundColor Gray
    }
}

# Create solution
Write-Host "Creating solution..." -ForegroundColor Yellow
$slnPath = Join-Path $RootPath "$ProjectName.sln"
if (!(Test-Path $slnPath)) {
    dotnet new sln -n $ProjectName -o $RootPath
}

# Create projects
Write-Host "Creating projects..." -ForegroundColor Yellow

# WPF project
$wpfPath = "src\$ProjectName.WPF"
if (!(Test-Path "$wpfPath\$ProjectName.WPF.csproj")) {
    dotnet new wpf -n "$ProjectName.WPF" -o $wpfPath
    dotnet sln add "$wpfPath\$ProjectName.WPF.csproj"
}

# Core library
$corePath = "src\$ProjectName.Core"
if (!(Test-Path "$corePath\$ProjectName.Core.csproj")) {
    dotnet new classlib -n "$ProjectName.Core" -o $corePath
    dotnet sln add "$corePath\$ProjectName.Core.csproj"
}

# Infrastructure library
$infraPath = "src\$ProjectName.Infrastructure"
if (!(Test-Path "$infraPath\$ProjectName.Infrastructure.csproj")) {
    dotnet new classlib -n "$ProjectName.Infrastructure" -o $infraPath
    dotnet sln add "$infraPath\$ProjectName.Infrastructure.csproj"
}

# WeChat library
$wechatPath = "src\$ProjectName.WeChat"
if (!(Test-Path "$wechatPath\$ProjectName.WeChat.csproj")) {
    dotnet new classlib -n "$ProjectName.WeChat" -o $wechatPath
    dotnet sln add "$wechatPath\$ProjectName.WeChat.csproj"
}

# Common library
$commonPath = "src\$ProjectName.Common"
if (!(Test-Path "$commonPath\$ProjectName.Common.csproj")) {
    dotnet new classlib -n "$ProjectName.Common" -o $commonPath
    dotnet sln add "$commonPath\$ProjectName.Common.csproj"
}

# Test project
$testPath = "tests\$ProjectName.Tests"
if (!(Test-Path "$testPath\$ProjectName.Tests.csproj")) {
    dotnet new xunit -n "$ProjectName.Tests" -o $testPath
    dotnet sln add "$testPath\$ProjectName.Tests.csproj"
}

Write-Host "Adding project references..." -ForegroundColor Yellow

# Add references
dotnet add "$wpfPath\$ProjectName.WPF.csproj" reference "$corePath\$ProjectName.Core.csproj"
dotnet add "$wpfPath\$ProjectName.WPF.csproj" reference "$infraPath\$ProjectName.Infrastructure.csproj"
dotnet add "$wpfPath\$ProjectName.WPF.csproj" reference "$wechatPath\$ProjectName.WeChat.csproj"
dotnet add "$wpfPath\$ProjectName.WPF.csproj" reference "$commonPath\$ProjectName.Common.csproj"

dotnet add "$infraPath\$ProjectName.Infrastructure.csproj" reference "$corePath\$ProjectName.Core.csproj"
dotnet add "$wechatPath\$ProjectName.WeChat.csproj" reference "$corePath\$ProjectName.Core.csproj"

Write-Host "Adding NuGet packages..." -ForegroundColor Yellow

# Add essential packages
dotnet add "$wpfPath\$ProjectName.WPF.csproj" package Microsoft.Extensions.Hosting
dotnet add "$wpfPath\$ProjectName.WPF.csproj" package Microsoft.Extensions.DependencyInjection
dotnet add "$wpfPath\$ProjectName.WPF.csproj" package CommunityToolkit.Mvvm

dotnet add "$infraPath\$ProjectName.Infrastructure.csproj" package Serilog
dotnet add "$infraPath\$ProjectName.Infrastructure.csproj" package Serilog.Sinks.File
dotnet add "$infraPath\$ProjectName.Infrastructure.csproj" package Microsoft.Extensions.Http
dotnet add "$infraPath\$ProjectName.Infrastructure.csproj" package Newtonsoft.Json

dotnet add "$testPath\$ProjectName.Tests.csproj" package Moq
dotnet add "$testPath\$ProjectName.Tests.csproj" package FluentAssertions

Write-Host "Creating configuration files..." -ForegroundColor Yellow

# Create appsettings.json
$appSettings = @"
{
  "WeChat": {
    "CorpId": "",
    "CorpSecret": "",
    "AgentId": "",
    "WebhookUrl": "http://localhost:8080/webhook"
  },
  "Api": {
    "BaseUrl": "",
    "Timeout": 30,
    "RetryCount": 3
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information"
    }
  }
}
"@

$appSettings | Out-File -FilePath "config\appsettings.json" -Encoding UTF8

Write-Host "Project initialization completed!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Open $ProjectName.sln with Visual Studio" -ForegroundColor White
Write-Host "2. Configure WeChat API settings" -ForegroundColor White
Write-Host "3. Start development" -ForegroundColor White
