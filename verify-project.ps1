# Project verification script
Write-Host "Verifying WeChat Forwarder project structure..." -ForegroundColor Green

$errors = @()
$warnings = @()

# Check solution file
if (Test-Path "WeChatForwarder.sln") {
    Write-Host "✓ Solution file exists" -ForegroundColor Green
} else {
    $errors += "Solution file missing"
}

# Check project files
$projects = @(
    "src/WeChatForwarder.WPF/WeChatForwarder.WPF.csproj",
    "src/WeChatForwarder.Core/WeChatForwarder.Core.csproj",
    "src/WeChatForwarder.Infrastructure/WeChatForwarder.Infrastructure.csproj",
    "src/WeChatForwarder.WeChat/WeChatForwarder.WeChat.csproj",
    "src/WeChatForwarder.Common/WeChatForwarder.Common.csproj",
    "tests/WeChatForwarder.Tests/WeChatForwarder.Tests.csproj"
)

foreach ($project in $projects) {
    if (Test-Path $project) {
        Write-Host "✓ $project" -ForegroundColor Green
    } else {
        $errors += "Project file missing: $project"
    }
}

# Check core source files
$coreFiles = @(
    "src/WeChatForwarder.Core/Models/Message.cs",
    "src/WeChatForwarder.Core/Models/GroupConfig.cs",
    "src/WeChatForwarder.Core/Models/ApiConfig.cs",
    "src/WeChatForwarder.Core/Interfaces/IMessageProcessor.cs",
    "src/WeChatForwarder.Core/Interfaces/IOrderNumberExtractor.cs",
    "src/WeChatForwarder.Core/Interfaces/IApiService.cs",
    "src/WeChatForwarder.Core/Interfaces/IForwardingService.cs",
    "src/WeChatForwarder.Core/Services/OrderNumberExtractor.cs",
    "src/WeChatForwarder.Core/Services/MessageProcessor.cs"
)

Write-Host "`nChecking core source files..." -ForegroundColor Yellow
foreach ($file in $coreFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file" -ForegroundColor Green
    } else {
        $errors += "Core file missing: $file"
    }
}

# Check infrastructure files
$infraFiles = @(
    "src/WeChatForwarder.Infrastructure/Configuration/ConfigurationManager.cs",
    "src/WeChatForwarder.Infrastructure/Http/HttpClientFactory.cs"
)

Write-Host "`nChecking infrastructure files..." -ForegroundColor Yellow
foreach ($file in $infraFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file" -ForegroundColor Green
    } else {
        $errors += "Infrastructure file missing: $file"
    }
}

# Check WPF files
$wpfFiles = @(
    "src/WeChatForwarder.WPF/App.xaml",
    "src/WeChatForwarder.WPF/App.xaml.cs",
    "src/WeChatForwarder.WPF/MainWindow.xaml",
    "src/WeChatForwarder.WPF/MainWindow.xaml.cs",
    "src/WeChatForwarder.WPF/ViewModels/MainViewModel.cs"
)

Write-Host "`nChecking WPF files..." -ForegroundColor Yellow
foreach ($file in $wpfFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file" -ForegroundColor Green
    } else {
        $errors += "WPF file missing: $file"
    }
}

# Check style files
$styleFiles = @(
    "src/WeChatForwarder.WPF/Resources/Styles/ButtonStyles.xaml",
    "src/WeChatForwarder.WPF/Resources/Styles/TextStyles.xaml"
)

Write-Host "`nChecking style files..." -ForegroundColor Yellow
foreach ($file in $styleFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file" -ForegroundColor Green
    } else {
        $warnings += "Style file missing: $file"
    }
}

# Check test files
$testFiles = @(
    "tests/WeChatForwarder.Tests/OrderNumberExtractorTests.cs"
)

Write-Host "`nChecking test files..." -ForegroundColor Yellow
foreach ($file in $testFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file" -ForegroundColor Green
    } else {
        $warnings += "Test file missing: $file"
    }
}

# Check configuration files
$configFiles = @(
    "config/appsettings.json"
)

Write-Host "`nChecking configuration files..." -ForegroundColor Yellow
foreach ($file in $configFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file" -ForegroundColor Green
    } else {
        $errors += "Configuration file missing: $file"
    }
}

# Check documentation
$docFiles = @(
    "README.md",
    "技术方案与开发计划.md",
    "项目结构设计.md"
)

Write-Host "`nChecking documentation..." -ForegroundColor Yellow
foreach ($file in $docFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file" -ForegroundColor Green
    } else {
        $warnings += "Documentation missing: $file"
    }
}

# Summary
Write-Host "`n" + "="*50 -ForegroundColor Cyan
Write-Host "VERIFICATION SUMMARY" -ForegroundColor Cyan
Write-Host "="*50 -ForegroundColor Cyan

if ($errors.Count -eq 0) {
    Write-Host "✓ All critical files are present!" -ForegroundColor Green
} else {
    Write-Host "✗ Found $($errors.Count) critical issues:" -ForegroundColor Red
    foreach ($error in $errors) {
        Write-Host "  - $error" -ForegroundColor Red
    }
}

if ($warnings.Count -gt 0) {
    Write-Host "⚠ Found $($warnings.Count) warnings:" -ForegroundColor Yellow
    foreach ($warning in $warnings) {
        Write-Host "  - $warning" -ForegroundColor Yellow
    }
}

# Project statistics
$totalFiles = Get-ChildItem -Recurse -File | Where-Object { $_.Extension -in @('.cs', '.xaml', '.csproj', '.sln', '.json', '.md') }
$csharpFiles = $totalFiles | Where-Object { $_.Extension -eq '.cs' }
$xamlFiles = $totalFiles | Where-Object { $_.Extension -eq '.xaml' }

Write-Host "`nProject Statistics:" -ForegroundColor Cyan
Write-Host "  Total relevant files: $($totalFiles.Count)" -ForegroundColor White
Write-Host "  C# files: $($csharpFiles.Count)" -ForegroundColor White
Write-Host "  XAML files: $($xamlFiles.Count)" -ForegroundColor White

# Next steps
Write-Host "`nNext Steps:" -ForegroundColor Cyan
Write-Host "1. Install .NET 7.0 SDK from https://dotnet.microsoft.com/download" -ForegroundColor White
Write-Host "2. Open WeChatForwarder.sln in Visual Studio 2022" -ForegroundColor White
Write-Host "3. Build the solution to verify all dependencies" -ForegroundColor White
Write-Host "4. Configure WeChat API settings in config/appsettings.json" -ForegroundColor White
Write-Host "5. Run the application and test basic functionality" -ForegroundColor White

if ($errors.Count -eq 0) {
    Write-Host "`n✓ Project structure verification completed successfully!" -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n✗ Project structure verification failed!" -ForegroundColor Red
    exit 1
}
