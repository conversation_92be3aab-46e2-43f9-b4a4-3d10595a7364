using Microsoft.Extensions.Logging;
using WeChatForwarder.Core.Interfaces;
using WeChatForwarder.Core.Models;

namespace WeChatForwarder.Core.Services
{
    /// <summary>
    /// 消息处理器实现
    /// </summary>
    public class MessageProcessor : IMessageProcessor
    {
        private readonly ILogger<MessageProcessor> _logger;
        private readonly IOrderNumberExtractor _orderNumberExtractor;
        private readonly IApiService _apiService;
        private readonly IForwardingService _forwardingService;

        public MessageProcessor(
            ILogger<MessageProcessor> logger,
            IOrderNumberExtractor orderNumberExtractor,
            IApiService apiService,
            IForwardingService forwardingService)
        {
            _logger = logger;
            _orderNumberExtractor = orderNumberExtractor;
            _apiService = apiService;
            _forwardingService = forwardingService;
        }

        /// <summary>
        /// 处理单个消息
        /// </summary>
        public async Task<MessageProcessResult> ProcessMessageAsync(Message message, CancellationToken cancellationToken = default)
        {
            var result = new MessageProcessResult
            {
                MessageId = message.Id,
                Status = MessageProcessStatus.Processing
            };

            try
            {
                _logger.LogInformation("开始处理消息: {MessageId}, 群组: {GroupId}", message.Id, message.GroupId);

                // 1. 提取订单号
                var orderNumber = await _orderNumberExtractor.ExtractOrderNumberAsync(message.Content, message.GroupId);
                if (string.IsNullOrWhiteSpace(orderNumber))
                {
                    _logger.LogDebug("消息中未找到订单号: {MessageId}", message.Id);
                    result.Status = MessageProcessStatus.Ignored;
                    result.Success = true;
                    return result;
                }

                result.ExtractedOrderNumber = orderNumber;
                message.ExtractedOrderNumber = orderNumber;

                _logger.LogInformation("提取到订单号: {OrderNumber}, 消息ID: {MessageId}", orderNumber, message.Id);

                // 2. 查询目标群组
                var apiResult = await _apiService.QueryTargetGroupsAsync(orderNumber, message.GroupId, cancellationToken);
                if (!apiResult.Success)
                {
                    _logger.LogWarning("API查询失败: {ErrorMessage}, 订单号: {OrderNumber}", apiResult.ErrorMessage, orderNumber);
                    result.Status = MessageProcessStatus.Failed;
                    result.ErrorMessage = apiResult.ErrorMessage;
                    result.Success = false;
                    return result;
                }

                if (apiResult.TargetGroupIds.Count == 0)
                {
                    _logger.LogInformation("未找到目标群组: 订单号: {OrderNumber}", orderNumber);
                    result.Status = MessageProcessStatus.Ignored;
                    result.Success = true;
                    return result;
                }

                result.TargetGroupIds = apiResult.TargetGroupIds;

                // 3. 转发消息
                var forwardingResult = await _forwardingService.ForwardMessageAsync(message, apiResult.TargetGroupIds, cancellationToken);
                if (forwardingResult.Success)
                {
                    _logger.LogInformation("消息转发成功: {MessageId}, 目标群组数: {Count}", message.Id, forwardingResult.SuccessfulGroupIds.Count);
                    result.Status = MessageProcessStatus.Forwarded;
                    result.Success = true;
                }
                else
                {
                    _logger.LogWarning("消息转发失败: {MessageId}, 错误: {ErrorMessage}", message.Id, forwardingResult.ErrorMessage);
                    result.Status = MessageProcessStatus.Failed;
                    result.ErrorMessage = forwardingResult.ErrorMessage;
                    result.Success = false;
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理消息时发生异常: {MessageId}", message.Id);
                result.Status = MessageProcessStatus.Failed;
                result.ErrorMessage = ex.Message;
                result.Success = false;
                return result;
            }
            finally
            {
                // 更新消息状态
                message.Status = result.Status;
                message.ErrorMessage = result.ErrorMessage;
            }
        }

        /// <summary>
        /// 批量处理消息
        /// </summary>
        public async Task<IEnumerable<MessageProcessResult>> ProcessMessagesAsync(IEnumerable<Message> messages, CancellationToken cancellationToken = default)
        {
            var results = new List<MessageProcessResult>();
            var messageList = messages.ToList();

            _logger.LogInformation("开始批量处理消息: {Count}条", messageList.Count);

            // 并行处理消息（可配置并发数）
            var semaphore = new SemaphoreSlim(Environment.ProcessorCount, Environment.ProcessorCount);
            var tasks = messageList.Select(async message =>
            {
                await semaphore.WaitAsync(cancellationToken);
                try
                {
                    return await ProcessMessageAsync(message, cancellationToken);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            results.AddRange(await Task.WhenAll(tasks));

            var successCount = results.Count(r => r.Success);
            _logger.LogInformation("批量处理完成: 总数: {Total}, 成功: {Success}, 失败: {Failed}", 
                results.Count, successCount, results.Count - successCount);

            return results;
        }
    }
}
