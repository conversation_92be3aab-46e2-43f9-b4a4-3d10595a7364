using Microsoft.Extensions.Logging;
using <PERSON>;
using Polly.Extensions.Http;
using System.Net;

namespace WeChatForwarder.Infrastructure.Http
{
    /// <summary>
    /// HTTP客户端工厂
    /// </summary>
    public class HttpClientFactory
    {
        private readonly ILogger<HttpClientFactory> _logger;
        private readonly HttpClient _httpClient;

        public HttpClientFactory(ILogger<HttpClientFactory> logger)
        {
            _logger = logger;
            _httpClient = CreateHttpClient();
        }

        /// <summary>
        /// 获取HTTP客户端
        /// </summary>
        public HttpClient GetClient()
        {
            return _httpClient;
        }

        /// <summary>
        /// 创建带重试策略的HTTP客户端
        /// </summary>
        private HttpClient CreateHttpClient()
        {
            var handler = new HttpClientHandler()
            {
                AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate
            };

            var httpClient = new HttpClient(handler)
            {
                Timeout = TimeSpan.FromSeconds(30)
            };

            // 设置默认请求头
            httpClient.DefaultRequestHeaders.Add("User-Agent", "WeChatForwarder/1.0");
            httpClient.DefaultRequestHeaders.Add("Accept", "application/json");

            return httpClient;
        }

        /// <summary>
        /// 创建重试策略
        /// </summary>
        public static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy(ILogger logger, int retryCount = 3)
        {
            return HttpPolicyExtensions
                .HandleTransientHttpError() // 处理HttpRequestException和5XX、408状态码
                .OrResult(msg => !msg.IsSuccessStatusCode) // 处理其他非成功状态码
                .WaitAndRetryAsync(
                    retryCount,
                    retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), // 指数退避
                    onRetry: (outcome, timespan, retryCount, context) =>
                    {
                        logger.LogWarning("HTTP请求重试 {RetryCount}/{MaxRetries}, 延迟: {Delay}ms, 原因: {Reason}",
                            retryCount, 3, timespan.TotalMilliseconds,
                            outcome.Exception?.Message ?? outcome.Result?.StatusCode.ToString());
                    });
        }

        /// <summary>
        /// 创建熔断器策略
        /// </summary>
        public static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicy(ILogger logger)
        {
            return HttpPolicyExtensions
                .HandleTransientHttpError()
                .CircuitBreakerAsync(
                    handledEventsAllowedBeforeBreaking: 5, // 连续5次失败后熔断
                    durationOfBreak: TimeSpan.FromSeconds(30), // 熔断30秒
                    onBreak: (exception, duration) =>
                    {
                        logger.LogWarning("HTTP熔断器开启，持续时间: {Duration}s, 原因: {Reason}",
                            duration.TotalSeconds, exception.Exception?.Message ?? exception.Result?.StatusCode.ToString());
                    },
                    onReset: () =>
                    {
                        logger.LogInformation("HTTP熔断器重置");
                    });
        }

        /// <summary>
        /// 创建组合策略（重试 + 熔断）
        /// </summary>
        public static IAsyncPolicy<HttpResponseMessage> GetCombinedPolicy(ILogger logger, int retryCount = 3)
        {
            var retryPolicy = GetRetryPolicy(logger, retryCount);
            var circuitBreakerPolicy = GetCircuitBreakerPolicy(logger);

            // 先重试，再熔断
            return Policy.WrapAsync(retryPolicy, circuitBreakerPolicy);
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }

    /// <summary>
    /// HTTP请求扩展方法
    /// </summary>
    public static class HttpClientExtensions
    {
        /// <summary>
        /// 发送JSON POST请求
        /// </summary>
        public static async Task<HttpResponseMessage> PostJsonAsync(this HttpClient client, string requestUri, object content, CancellationToken cancellationToken = default)
        {
            var json = Newtonsoft.Json.JsonConvert.SerializeObject(content);
            var httpContent = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
            return await client.PostAsync(requestUri, httpContent, cancellationToken);
        }

        /// <summary>
        /// 发送GET请求并反序列化响应
        /// </summary>
        public static async Task<T?> GetJsonAsync<T>(this HttpClient client, string requestUri, CancellationToken cancellationToken = default)
        {
            var response = await client.GetAsync(requestUri, cancellationToken);
            response.EnsureSuccessStatusCode();
            
            var json = await response.Content.ReadAsStringAsync(cancellationToken);
            return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(json);
        }

        /// <summary>
        /// 发送POST请求并反序列化响应
        /// </summary>
        public static async Task<T?> PostJsonAsync<T>(this HttpClient client, string requestUri, object content, CancellationToken cancellationToken = default)
        {
            var response = await client.PostJsonAsync(requestUri, content, cancellationToken);
            response.EnsureSuccessStatusCode();
            
            var json = await response.Content.ReadAsStringAsync(cancellationToken);
            return Newtonsoft.Json.JsonConvert.DeserializeObject<T>(json);
        }
    }
}
