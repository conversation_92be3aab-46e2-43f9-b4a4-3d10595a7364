using WeChatForwarder.Core.Models;

namespace WeChatForwarder.Core.Interfaces
{
    /// <summary>
    /// 消息转发服务接口
    /// </summary>
    public interface IForwardingService
    {
        /// <summary>
        /// 转发消息到指定群组
        /// </summary>
        /// <param name="message">原始消息</param>
        /// <param name="targetGroupIds">目标群组ID列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>转发结果</returns>
        Task<ForwardingResult> ForwardMessageAsync(Message message, IEnumerable<string> targetGroupIds, CancellationToken cancellationToken = default);

        /// <summary>
        /// 批量转发消息
        /// </summary>
        /// <param name="forwardingTasks">转发任务列表</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>转发结果列表</returns>
        Task<IEnumerable<ForwardingResult>> ForwardMessagesAsync(IEnumerable<ForwardingTask> forwardingTasks, CancellationToken cancellationToken = default);

        /// <summary>
        /// 获取转发状态
        /// </summary>
        /// <param name="messageId">消息ID</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>转发状态</returns>
        Task<ForwardingStatus?> GetForwardingStatusAsync(string messageId, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 转发任务
    /// </summary>
    public class ForwardingTask
    {
        /// <summary>
        /// 原始消息
        /// </summary>
        public Message Message { get; set; } = new();

        /// <summary>
        /// 目标群组ID列表
        /// </summary>
        public List<string> TargetGroupIds { get; set; } = new();

        /// <summary>
        /// 任务优先级
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 转发结果
    /// </summary>
    public class ForwardingResult
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public string MessageId { get; set; } = string.Empty;

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 成功转发的群组ID列表
        /// </summary>
        public List<string> SuccessfulGroupIds { get; set; } = new();

        /// <summary>
        /// 失败的群组ID列表
        /// </summary>
        public List<string> FailedGroupIds { get; set; } = new();

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 转发时间
        /// </summary>
        public DateTime ForwardedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 详细结果
        /// </summary>
        public List<GroupForwardingResult> DetailedResults { get; set; } = new();
    }

    /// <summary>
    /// 群组转发结果
    /// </summary>
    public class GroupForwardingResult
    {
        /// <summary>
        /// 群组ID
        /// </summary>
        public string GroupId { get; set; } = string.Empty;

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 转发的消息ID
        /// </summary>
        public string? ForwardedMessageId { get; set; }
    }

    /// <summary>
    /// 转发状态
    /// </summary>
    public class ForwardingStatus
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public string MessageId { get; set; } = string.Empty;

        /// <summary>
        /// 状态
        /// </summary>
        public ForwardingStatusType Status { get; set; }

        /// <summary>
        /// 总目标群组数
        /// </summary>
        public int TotalTargetGroups { get; set; }

        /// <summary>
        /// 成功转发数
        /// </summary>
        public int SuccessfulForwards { get; set; }

        /// <summary>
        /// 失败转发数
        /// </summary>
        public int FailedForwards { get; set; }

        /// <summary>
        /// 最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 转发状态类型
    /// </summary>
    public enum ForwardingStatusType
    {
        /// <summary>
        /// 待转发
        /// </summary>
        Pending = 0,

        /// <summary>
        /// 转发中
        /// </summary>
        InProgress = 1,

        /// <summary>
        /// 已完成
        /// </summary>
        Completed = 2,

        /// <summary>
        /// 部分失败
        /// </summary>
        PartiallyFailed = 3,

        /// <summary>
        /// 完全失败
        /// </summary>
        Failed = 4
    }
}
