using System.Collections.Generic;

namespace WeChatForwarder.Core.Models
{
    /// <summary>
    /// 群组配置
    /// </summary>
    public class GroupConfig
    {
        /// <summary>
        /// 群组ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 群组名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 企业微信群聊ID
        /// </summary>
        public string ChatId { get; set; } = string.Empty;

        /// <summary>
        /// 是否为源群组（监控的群组）
        /// </summary>
        public bool IsSource { get; set; }

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 群组描述
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 转发规则配置
    /// </summary>
    public class ForwardingRule
    {
        /// <summary>
        /// 规则ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 规则名称
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 源群组ID
        /// </summary>
        public string SourceGroupId { get; set; } = string.Empty;

        /// <summary>
        /// 订单号匹配模式（正则表达式）
        /// </summary>
        public string OrderNumberPattern { get; set; } = string.Empty;

        /// <summary>
        /// API查询端点
        /// </summary>
        public string ApiEndpoint { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 优先级（数字越小优先级越高）
        /// </summary>
        public int Priority { get; set; } = 0;

        /// <summary>
        /// 规则描述
        /// </summary>
        public string? Description { get; set; }
    }

    /// <summary>
    /// 群组配置集合
    /// </summary>
    public class GroupConfiguration
    {
        /// <summary>
        /// 群组列表
        /// </summary>
        public List<GroupConfig> Groups { get; set; } = new();

        /// <summary>
        /// 转发规则列表
        /// </summary>
        public List<ForwardingRule> ForwardingRules { get; set; } = new();
    }
}
