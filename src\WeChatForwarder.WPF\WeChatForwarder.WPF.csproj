<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net7.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <AssemblyTitle>企业微信群转发助手</AssemblyTitle>
    <AssemblyDescription>企业微信群消息智能转发工具</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="7.0.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.0" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\WeChatForwarder.Core\WeChatForwarder.Core.csproj" />
    <ProjectReference Include="..\WeChatForwarder.Infrastructure\WeChatForwarder.Infrastructure.csproj" />
    <ProjectReference Include="..\WeChatForwarder.WeChat\WeChatForwarder.WeChat.csproj" />
    <ProjectReference Include="..\WeChatForwarder.Common\WeChatForwarder.Common.csproj" />
  </ItemGroup>

</Project>
