<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <AssemblyTitle>WeChatForwarder.Infrastructure</AssemblyTitle>
    <AssemblyDescription>企业微信群转发助手基础设施层</AssemblyDescription>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Http" Version="7.0.0" />
    <PackageReference Include="Polly" Version="7.2.4" />
    <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
    <PackageReference Include="Serilog" Version="3.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="4.1.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="7.0.10" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\WeChatForwarder.Core\WeChatForwarder.Core.csproj" />
    <ProjectReference Include="..\WeChatForwarder.Common\WeChatForwarder.Common.csproj" />
  </ItemGroup>

</Project>
