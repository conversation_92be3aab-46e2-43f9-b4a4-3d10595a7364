namespace WeChatForwarder.Core.Models
{
    /// <summary>
    /// API配置
    /// </summary>
    public class ApiConfig
    {
        /// <summary>
        /// API基础URL
        /// </summary>
        public string BaseUrl { get; set; } = string.Empty;

        /// <summary>
        /// 超时时间（秒）
        /// </summary>
        public int Timeout { get; set; } = 30;

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; } = 3;

        /// <summary>
        /// API密钥
        /// </summary>
        public string? ApiKey { get; set; }

        /// <summary>
        /// 认证令牌
        /// </summary>
        public string? AuthToken { get; set; }
    }

    /// <summary>
    /// 企业微信配置
    /// </summary>
    public class WeChatConfig
    {
        /// <summary>
        /// 企业ID
        /// </summary>
        public string CorpId { get; set; } = string.Empty;

        /// <summary>
        /// 企业密钥
        /// </summary>
        public string CorpSecret { get; set; } = string.Empty;

        /// <summary>
        /// 应用ID
        /// </summary>
        public string AgentId { get; set; } = string.Empty;

        /// <summary>
        /// Webhook接收地址
        /// </summary>
        public string WebhookUrl { get; set; } = string.Empty;

        /// <summary>
        /// Token（用于验证消息来源）
        /// </summary>
        public string? Token { get; set; }

        /// <summary>
        /// EncodingAESKey（用于消息加解密）
        /// </summary>
        public string? EncodingAESKey { get; set; }
    }

    /// <summary>
    /// API查询结果
    /// </summary>
    public class ApiQueryResult
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 目标群组ID列表
        /// </summary>
        public List<string> TargetGroupIds { get; set; } = new();

        /// <summary>
        /// 错误消息
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 响应数据
        /// </summary>
        public object? Data { get; set; }
    }
}
